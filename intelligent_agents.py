#!/usr/bin/env python3
"""
Intelligent Agents for Accela Knowledge Base
Multi-agent system that collaborates to find optimal solutions across counties
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np
import re
from agentic_graph_system import <PERSON><PERSON><PERSON>KnowledgeGraph, AgentTask, AgentResponse, AgentRole

# Optional LLM integration - only use if available and beneficial
try:
    from openai import OpenAI
    LLM_AVAILABLE = True
except ImportError:
    LLM_AVAILABLE = False

class LLMHelper:
    """Helper class for strategic LLM usage - only where it adds real value"""

    def __init__(self, api_key: str = None):
        self.available = LLM_AVAILABLE and api_key is not None
        self.client = None
        if self.available:
            try:
                self.client = OpenAI(api_key=api_key)
            except Exception:
                self.available = False

    def analyze_code_semantics(self, code_content: str, context: str = "") -> Dict[str, Any]:
        """Use LLM to understand code semantics - genuinely useful for code analysis"""
        if not self.available or not self.client or len(code_content) > 2000:  # Don't waste tokens on huge files
            return {"semantic_analysis": "LLM not available or content too large", "confidence": 0.0}

        try:
            prompt = f"""Analyze this Accela JavaScript code and provide semantic insights:

Context: {context}

Code:
{code_content[:1000]}...

Provide a JSON response with:
1. "purpose": What this script actually does (1-2 sentences)
2. "business_logic": Key business rules implemented
3. "integration_points": External systems or APIs used
4. "complexity_factors": What makes this code complex
5. "improvement_suggestions": Specific technical improvements

Keep response concise and technical."""

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=300,
                temperature=0.1
            )

            result = json.loads(response.choices[0].message.content)
            result["confidence"] = 0.8
            return result

        except Exception as e:
            return {"semantic_analysis": f"LLM analysis failed: {str(e)}", "confidence": 0.0}

    def generate_implementation_reasoning(self, implementations: List[Dict], query: str) -> str:
        """Use LLM to generate human-readable reasoning - valuable for explanations"""
        if not self.available or not self.client or len(implementations) == 0:
            return "LLM reasoning not available"

        try:
            # Prepare concise implementation summaries
            impl_summaries = []
            for impl in implementations[:3]:  # Only top 3 to save tokens
                summary = f"County: {impl.get('county', 'Unknown')}, "
                summary += f"Score: {impl.get('score', 0):.2f}, "
                summary += f"Functions: {', '.join(impl.get('metadata', {}).get('functions', [])[:3])}"
                impl_summaries.append(summary)

            prompt = f"""Given this query: "{query}"

And these top implementations:
{chr(10).join(f"{i+1}. {summary}" for i, summary in enumerate(impl_summaries))}

Provide a clear, technical explanation of why the top implementation is recommended. Focus on:
1. Technical merits
2. Functional completeness
3. Implementation quality
4. Specific advantages over alternatives

Keep response under 100 words and technical."""

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=150,
                temperature=0.2
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"Could not generate LLM reasoning: {str(e)}"

class BaseAgent(ABC):
    """Base class for all intelligent agents"""

    def __init__(self, name: str, role: AgentRole, knowledge_graph: AccelaKnowledgeGraph, llm_helper: LLMHelper = None):
        self.name = name
        self.role = role
        self.knowledge_graph = knowledge_graph
        self.memory = {}  # Agent's working memory
        self.llm_helper = llm_helper or LLMHelper()  # Optional LLM assistance

    @abstractmethod
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a specific task"""
        pass

    def update_memory(self, key: str, value: Any):
        """Update agent's memory"""
        self.memory[key] = value

    def get_memory(self, key: str, default=None):
        """Get value from agent's memory"""
        return self.memory.get(key, default)

class AnalyzerAgent(BaseAgent):
    """Agent that analyzes code patterns and relationships with optional LLM intelligence"""

    def __init__(self, knowledge_graph: AccelaKnowledgeGraph, llm_helper: LLMHelper = None):
        super().__init__("CodeAnalyzer", AgentRole.ANALYZER, knowledge_graph, llm_helper)
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Analyze code patterns for a given query"""
        
        query = task.query
        context = task.context
        
        # Analyze patterns in the knowledge graph
        analysis_result = {
            'patterns_found': [],
            'complexity_analysis': {},
            'function_usage': {},
            'county_coverage': {}
        }
        
        # Find relevant scripts
        relevant_scripts = self._find_relevant_scripts(query)
        
        # Analyze complexity patterns
        complexity_analysis = self._analyze_complexity_patterns(relevant_scripts)
        analysis_result['complexity_analysis'] = complexity_analysis
        
        # Analyze function usage patterns
        function_usage = self._analyze_function_usage(relevant_scripts)
        analysis_result['function_usage'] = function_usage
        
        # Analyze county coverage
        county_coverage = self._analyze_county_coverage(relevant_scripts, query)
        analysis_result['county_coverage'] = county_coverage
        
        # Generate reasoning
        reasoning = self._generate_analysis_reasoning(analysis_result)
        
        return AgentResponse(
            task_id=task.task_id,
            agent_role=self.role,
            result=analysis_result,
            confidence=0.8,
            reasoning=reasoning,
            recommendations=self._generate_analysis_recommendations(analysis_result)
        )
    
    def _find_relevant_scripts(self, query: str) -> List[str]:
        """Find scripts relevant to the query with enhanced semantic understanding"""
        relevant_scripts = []

        # Extract key terms from query for better matching
        query_terms = self._extract_query_terms(query)

        for node in self.knowledge_graph.graph.nodes():
            if node.startswith("script:"):
                node_data = self.knowledge_graph.graph.nodes[node]
                relevance_score = 0

                # Enhanced relevance scoring
                # 1. Direct keyword matching
                for term in query_terms:
                    if term in str(node_data.get('module', '')).lower():
                        relevance_score += 2
                    if term in str(node_data.get('application_type', '')).lower():
                        relevance_score += 2
                    if term in str(node_data.get('event_prefix', '')).lower():
                        relevance_score += 1
                    if any(term in func.lower() for func in node_data.get('functions', [])):
                        relevance_score += 3

                # 2. Use LLM for semantic analysis of complex scripts (only when beneficial)
                if relevance_score > 0 and self.llm_helper.available:
                    content = node_data.get('content', '')
                    if content and len(content) > 500:  # Only for substantial scripts
                        semantic_analysis = self.llm_helper.analyze_code_semantics(
                            content, f"Query context: {query}"
                        )
                        if semantic_analysis.get('confidence', 0) > 0.5:
                            # Boost score if LLM finds semantic relevance
                            purpose = semantic_analysis.get('purpose', '').lower()
                            if any(term in purpose for term in query_terms):
                                relevance_score += 2

                if relevance_score >= 2:  # Threshold for relevance
                    relevant_scripts.append(node)

        return relevant_scripts

    def _extract_query_terms(self, query: str) -> List[str]:
        """Extract meaningful terms from query"""
        # Remove common words and extract key terms
        stop_words = {'how', 'to', 'do', 'i', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'for', 'with'}
        terms = [word.lower().strip() for word in query.split() if len(word) > 2]
        return [term for term in terms if term not in stop_words]
    
    def _analyze_complexity_patterns(self, scripts: List[str]) -> Dict[str, Any]:
        """Analyze complexity patterns across scripts"""
        
        complexity_counts = {'low': 0, 'medium': 0, 'high': 0}
        county_complexity = {}
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            complexity = node_data.get('complexity', 'medium')
            county = node_data.get('county', 'unknown')
            
            complexity_counts[complexity] += 1
            
            if county not in county_complexity:
                county_complexity[county] = {'low': 0, 'medium': 0, 'high': 0}
            county_complexity[county][complexity] += 1
        
        return {
            'overall_distribution': complexity_counts,
            'by_county': county_complexity,
            'recommendation': self._get_complexity_recommendation(complexity_counts)
        }
    
    def _analyze_function_usage(self, scripts: List[str]) -> Dict[str, Any]:
        """Analyze function usage patterns"""
        
        function_counts = {}
        county_functions = {}
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            county = node_data.get('county', 'unknown')
            functions = node_data.get('functions', [])
            
            if county not in county_functions:
                county_functions[county] = set()
            
            for func in functions:
                function_counts[func] = function_counts.get(func, 0) + 1
                county_functions[county].add(func)
        
        # Find most common functions
        sorted_functions = sorted(function_counts.items(), key=lambda x: x[1], reverse=True)
        
        return {
            'most_common_functions': sorted_functions[:10],
            'total_unique_functions': len(function_counts),
            'county_function_diversity': {county: len(funcs) for county, funcs in county_functions.items()},
            'cross_county_functions': self._find_cross_county_functions(county_functions)
        }
    
    def _analyze_county_coverage(self, scripts: List[str], query: str) -> Dict[str, Any]:
        """Analyze which counties have implementations for the query"""
        
        county_scripts = {}
        county_quality = {}
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            county = node_data.get('county', 'unknown')
            
            if county not in county_scripts:
                county_scripts[county] = []
                county_quality[county] = {'excellent': 0, 'good': 0, 'poor': 0}
            
            county_scripts[county].append(script)
            doc_quality = node_data.get('doc_quality', 'poor')
            county_quality[county][doc_quality] += 1
        
        return {
            'counties_with_implementations': list(county_scripts.keys()),
            'implementation_counts': {county: len(scripts) for county, scripts in county_scripts.items()},
            'quality_distribution': county_quality,
            'coverage_percentage': len(county_scripts) / max(1, len(self._get_all_counties())) * 100
        }
    
    def _get_all_counties(self) -> List[str]:
        """Get all counties in the knowledge graph"""
        return [node.split(":")[1] for node in self.knowledge_graph.graph.nodes() 
                if node.startswith("county:")]
    
    def _find_cross_county_functions(self, county_functions: Dict[str, set]) -> List[str]:
        """Find functions used across multiple counties"""
        function_counties = {}
        
        for county, functions in county_functions.items():
            for func in functions:
                if func not in function_counties:
                    function_counties[func] = set()
                function_counties[func].add(county)
        
        # Return functions used by 2+ counties
        return [func for func, counties in function_counties.items() if len(counties) >= 2]
    
    def _get_complexity_recommendation(self, complexity_counts: Dict[str, int]) -> str:
        """Generate complexity recommendation"""
        total = sum(complexity_counts.values())
        if total == 0:
            return "No data available"
        
        high_percentage = complexity_counts['high'] / total * 100
        
        if high_percentage > 50:
            return "High complexity dominates - consider simplification"
        elif high_percentage < 20:
            return "Good complexity distribution - mostly low/medium"
        else:
            return "Mixed complexity - review high complexity implementations"
    
    def _generate_analysis_reasoning(self, analysis: Dict[str, Any]) -> str:
        """Generate reasoning for the analysis"""
        
        reasoning_parts = []
        
        # Complexity reasoning
        complexity = analysis['complexity_analysis']
        reasoning_parts.append(f"Complexity Analysis: {complexity['recommendation']}")
        
        # Function usage reasoning
        function_usage = analysis['function_usage']
        reasoning_parts.append(f"Found {function_usage['total_unique_functions']} unique functions")
        reasoning_parts.append(f"Top function: {function_usage['most_common_functions'][0][0] if function_usage['most_common_functions'] else 'None'}")
        
        # Coverage reasoning
        coverage = analysis['county_coverage']
        reasoning_parts.append(f"Coverage: {coverage['coverage_percentage']:.1f}% of counties have implementations")
        
        return " | ".join(reasoning_parts)
    
    def _generate_analysis_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis"""
        
        recommendations = []
        
        # Complexity recommendations
        complexity = analysis['complexity_analysis']
        if "simplification" in complexity['recommendation']:
            recommendations.append("Consider refactoring high-complexity implementations")
        
        # Function recommendations
        function_usage = analysis['function_usage']
        if function_usage['cross_county_functions']:
            recommendations.append(f"Standardize common functions: {', '.join(function_usage['cross_county_functions'][:3])}")
        
        # Coverage recommendations
        coverage = analysis['county_coverage']
        if coverage['coverage_percentage'] < 50:
            recommendations.append("Low implementation coverage - opportunity for knowledge sharing")
        
        return recommendations

class ComparatorAgent(BaseAgent):
    """Agent that compares implementations across counties"""
    
    def __init__(self, knowledge_graph: AccelaKnowledgeGraph):
        super().__init__("CountyComparator", AgentRole.COMPARATOR, knowledge_graph)
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Compare implementations across counties"""
        
        query = task.query
        counties = task.context.get('counties', [])
        
        if not counties:
            counties = self._get_all_counties()
        
        comparison_result = {
            'county_comparisons': {},
            'best_practices': [],
            'gaps_identified': [],
            'similarity_matrix': {}
        }
        
        # Compare each county
        for county in counties:
            county_analysis = self._analyze_county_implementation(county, query)
            comparison_result['county_comparisons'][county] = county_analysis
        
        # Find best practices
        best_practices = self._identify_best_practices(comparison_result['county_comparisons'])
        comparison_result['best_practices'] = best_practices
        
        # Identify gaps
        gaps = self._identify_gaps(comparison_result['county_comparisons'])
        comparison_result['gaps_identified'] = gaps
        
        # Calculate similarity matrix
        similarity_matrix = self._calculate_similarity_matrix(counties)
        comparison_result['similarity_matrix'] = similarity_matrix
        
        reasoning = self._generate_comparison_reasoning(comparison_result)
        
        return AgentResponse(
            task_id=task.task_id,
            agent_role=self.role,
            result=comparison_result,
            confidence=0.85,
            reasoning=reasoning,
            recommendations=self._generate_comparison_recommendations(comparison_result)
        )
    
    def _analyze_county_implementation(self, county: str, query: str) -> Dict[str, Any]:
        """Analyze a specific county's implementation"""
        
        county_node = f"county:{county}"
        if not self.knowledge_graph.graph.has_node(county_node):
            return {'error': f'County {county} not found'}
        
        # Get all scripts for this county
        county_scripts = []
        for script_node in self.knowledge_graph.graph.successors(county_node):
            if script_node.startswith("script:"):
                county_scripts.append(script_node)
        
        # Filter relevant scripts
        relevant_scripts = [script for script in county_scripts 
                          if self._is_script_relevant(script, query)]
        
        if not relevant_scripts:
            return {
                'implementation_count': 0,
                'quality_score': 0,
                'functions_used': [],
                'complexity_distribution': {},
                'strengths': [],
                'weaknesses': ['No relevant implementations found']
            }
        
        # Analyze the relevant scripts
        analysis = {
            'implementation_count': len(relevant_scripts),
            'quality_score': self._calculate_quality_score(relevant_scripts),
            'functions_used': self._get_functions_used(relevant_scripts),
            'complexity_distribution': self._get_complexity_distribution(relevant_scripts),
            'strengths': self._identify_strengths(relevant_scripts),
            'weaknesses': self._identify_weaknesses(relevant_scripts)
        }
        
        return analysis
    
    def _is_script_relevant(self, script_node: str, query: str) -> bool:
        """Check if a script is relevant to the query"""
        node_data = self.knowledge_graph.graph.nodes[script_node]
        
        return (query.lower() in str(node_data.get('module', '')).lower() or
                query.lower() in str(node_data.get('script_type', '')).lower() or
                any(query.lower() in func.lower() for func in node_data.get('functions', [])))
    
    def _calculate_quality_score(self, scripts: List[str]) -> float:
        """Calculate overall quality score for scripts"""
        if not scripts:
            return 0.0
        
        total_score = 0.0
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            
            # Complexity score (lower complexity = higher score)
            complexity_scores = {'low': 1.0, 'medium': 0.7, 'high': 0.4}
            complexity_score = complexity_scores.get(node_data.get('complexity', 'medium'), 0.7)
            
            # Documentation score
            doc_scores = {'excellent': 1.0, 'good': 0.7, 'poor': 0.3}
            doc_score = doc_scores.get(node_data.get('doc_quality', 'poor'), 0.3)
            
            # Function count score (more functions = more comprehensive)
            func_count = len(node_data.get('functions', []))
            func_score = min(func_count / 10, 1.0)  # Normalize to 0-1
            
            script_score = (complexity_score * 0.4 + doc_score * 0.4 + func_score * 0.2)
            total_score += script_score
        
        return total_score / len(scripts)
    
    def _get_functions_used(self, scripts: List[str]) -> List[str]:
        """Get all functions used in the scripts"""
        functions = set()
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            functions.update(node_data.get('functions', []))
        
        return list(functions)
    
    def _get_complexity_distribution(self, scripts: List[str]) -> Dict[str, int]:
        """Get complexity distribution of scripts"""
        distribution = {'low': 0, 'medium': 0, 'high': 0}
        
        for script in scripts:
            node_data = self.knowledge_graph.graph.nodes[script]
            complexity = node_data.get('complexity', 'medium')
            distribution[complexity] += 1
        
        return distribution
    
    def _identify_strengths(self, scripts: List[str]) -> List[str]:
        """Identify strengths of the implementation"""
        strengths = []
        
        # Check for good documentation
        excellent_docs = sum(1 for script in scripts 
                           if self.knowledge_graph.graph.nodes[script].get('doc_quality') == 'excellent')
        if excellent_docs > len(scripts) * 0.5:
            strengths.append("Well-documented implementations")
        
        # Check for low complexity
        low_complexity = sum(1 for script in scripts 
                           if self.knowledge_graph.graph.nodes[script].get('complexity') == 'low')
        if low_complexity > len(scripts) * 0.6:
            strengths.append("Low complexity implementations")
        
        # Check for comprehensive function coverage
        total_functions = sum(len(self.knowledge_graph.graph.nodes[script].get('functions', [])) 
                            for script in scripts)
        if total_functions > len(scripts) * 5:  # Average > 5 functions per script
            strengths.append("Comprehensive function coverage")
        
        return strengths
    
    def _identify_weaknesses(self, scripts: List[str]) -> List[str]:
        """Identify weaknesses of the implementation"""
        weaknesses = []
        
        # Check for poor documentation
        poor_docs = sum(1 for script in scripts 
                       if self.knowledge_graph.graph.nodes[script].get('doc_quality') == 'poor')
        if poor_docs > len(scripts) * 0.5:
            weaknesses.append("Poor documentation quality")
        
        # Check for high complexity
        high_complexity = sum(1 for script in scripts 
                            if self.knowledge_graph.graph.nodes[script].get('complexity') == 'high')
        if high_complexity > len(scripts) * 0.3:
            weaknesses.append("High complexity implementations")
        
        # Check for limited function usage
        total_functions = sum(len(self.knowledge_graph.graph.nodes[script].get('functions', [])) 
                            for script in scripts)
        if total_functions < len(scripts) * 2:  # Average < 2 functions per script
            weaknesses.append("Limited function usage")
        
        return weaknesses
    
    def _identify_best_practices(self, county_comparisons: Dict[str, Dict]) -> List[Dict[str, Any]]:
        """Identify best practices across counties"""
        best_practices = []
        
        # Find county with highest quality score
        best_county = max(county_comparisons.items(), 
                         key=lambda x: x[1].get('quality_score', 0))
        
        if best_county[1].get('quality_score', 0) > 0.7:
            best_practices.append({
                'practice': f"Follow {best_county[0]}'s implementation approach",
                'reason': f"Highest quality score: {best_county[1]['quality_score']:.2f}",
                'county': best_county[0]
            })
        
        # Find most commonly used functions
        all_functions = {}
        for county, data in county_comparisons.items():
            for func in data.get('functions_used', []):
                all_functions[func] = all_functions.get(func, 0) + 1
        
        common_functions = [func for func, count in all_functions.items() if count >= 2]
        if common_functions:
            best_practices.append({
                'practice': "Use standardized functions",
                'reason': f"Functions used by multiple counties: {', '.join(common_functions[:3])}",
                'functions': common_functions
            })
        
        return best_practices
    
    def _identify_gaps(self, county_comparisons: Dict[str, Dict]) -> List[Dict[str, Any]]:
        """Identify gaps in implementations"""
        gaps = []
        
        # Find counties with no implementations
        no_implementation = [county for county, data in county_comparisons.items() 
                           if data.get('implementation_count', 0) == 0]
        
        if no_implementation:
            gaps.append({
                'type': 'missing_implementation',
                'counties': no_implementation,
                'description': 'Counties with no relevant implementations'
            })
        
        # Find counties with poor quality
        poor_quality = [county for county, data in county_comparisons.items() 
                       if data.get('quality_score', 0) < 0.4]
        
        if poor_quality:
            gaps.append({
                'type': 'poor_quality',
                'counties': poor_quality,
                'description': 'Counties with low-quality implementations'
            })
        
        return gaps
    
    def _calculate_similarity_matrix(self, counties: List[str]) -> Dict[str, Dict[str, float]]:
        """Calculate similarity matrix between counties"""
        similarity_matrix = {}
        
        for i, county1 in enumerate(counties):
            similarity_matrix[county1] = {}
            for county2 in counties:
                if county1 == county2:
                    similarity_matrix[county1][county2] = 1.0
                else:
                    # Use graph-based similarity if available
                    county1_node = f"county:{county1}"
                    county2_node = f"county:{county2}"
                    
                    if (self.knowledge_graph.graph.has_edge(county1_node, county2_node) and
                        self.knowledge_graph.graph[county1_node][county2_node].get('relationship') == 'similar_to'):
                        similarity = self.knowledge_graph.graph[county1_node][county2_node].get('weight', 0.5)
                    else:
                        similarity = 0.0
                    
                    similarity_matrix[county1][county2] = similarity
        
        return similarity_matrix
    
    def _get_all_counties(self) -> List[str]:
        """Get all counties in the knowledge graph"""
        return [node.split(":")[1] for node in self.knowledge_graph.graph.nodes() 
                if node.startswith("county:")]
    
    def _generate_comparison_reasoning(self, comparison: Dict[str, Any]) -> str:
        """Generate reasoning for the comparison"""
        
        reasoning_parts = []
        
        # Best practices reasoning
        best_practices = comparison['best_practices']
        if best_practices:
            reasoning_parts.append(f"Found {len(best_practices)} best practices")
        
        # Gaps reasoning
        gaps = comparison['gaps_identified']
        if gaps:
            reasoning_parts.append(f"Identified {len(gaps)} implementation gaps")
        
        # Quality reasoning
        county_comparisons = comparison['county_comparisons']
        avg_quality = np.mean([data.get('quality_score', 0) for data in county_comparisons.values()])
        reasoning_parts.append(f"Average quality score: {avg_quality:.2f}")
        
        return " | ".join(reasoning_parts)
    
    def _generate_comparison_recommendations(self, comparison: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on comparison"""
        
        recommendations = []
        
        # Best practice recommendations
        for practice in comparison['best_practices']:
            recommendations.append(f"Adopt best practice: {practice['practice']}")
        
        # Gap recommendations
        for gap in comparison['gaps_identified']:
            if gap['type'] == 'missing_implementation':
                recommendations.append(f"Implement solutions for: {', '.join(gap['counties'])}")
            elif gap['type'] == 'poor_quality':
                recommendations.append(f"Improve quality for: {', '.join(gap['counties'])}")
        
        return recommendations
