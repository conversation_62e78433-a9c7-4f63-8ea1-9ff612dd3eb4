# Accela Naming Convention Integration

## 🏷️ Overview

The enhanced agentic knowledge base now fully parses and utilizes <PERSON><PERSON><PERSON>'s script naming convention to create a much more sophisticated understanding of your implementations across counties.

## 📋 Accela Naming Convention Format

Accela scripts follow the pattern: **`aaaa;b!c!d!e.js`**

Where:
- **`aaaa`** = Event Prefix (e.g., ASA, WTUA, IRSA)
- **`b`** = Module (e.g., Licenses, Permits, Planning)
- **`c`** = Application Type (e.g., Building, Business, Case)
- **`d`** = Sub Type (e.g., New, Amendment, Express)
- **`e`** = Category (e.g., Application, Renewal, License)
- **`~`** = Wildcard (matches any value)

## 🎯 Event Prefix Meanings

The system recognizes these common event prefixes:

| Prefix | Full Name | Description |
|--------|-----------|-------------|
| **ASA** | Application Submittal After | Triggered after application submission |
| **ASB** | Application Submittal Before | Triggered before application submission |
| **ASUA** | Application Status Update After | Triggered after status changes |
| **WTUA** | Workflow Task Update After | Triggered after workflow task updates |
| **WTUB** | Workflow Task Update Before | Triggered before workflow task updates |
| **ISA** | Inspection Schedule After | Triggered after inspection scheduling |
| **IRSA** | Inspection Result Submit After | Triggered after inspection results |
| **IFA** | Inspection Finalize After | Triggered after inspection finalization |
| **PRA** | Payment Receive After | Triggered after payment processing |
| **FAA** | Fee Assess After | Triggered after fee assessment |
| **CAA** | Condition Approve After | Triggered after condition approval |
| **CTRCA** | Convert To Real Cap After | Triggered after permit conversion |

## 🕸️ Enhanced Knowledge Graph Structure

### New Node Types:
1. **Event Prefix Nodes** (`event_prefix:ASA`)
   - Usage count across counties
   - Description and purpose
   - Counties that use this prefix

2. **Accela Module Nodes** (`accela_module:Licenses`)
   - Module usage statistics
   - Counties implementing this module
   - Related application types

3. **Application Type Nodes** (`app_type:Building`)
   - Application type coverage
   - Counties handling this type
   - Associated modules and prefixes

### Enhanced Relationships:
- **Script → Event Prefix**: `uses_event_prefix`
- **Script → Module**: `targets_module`
- **Script → Application Type**: `handles_app_type`
- **County → County**: `shares_event_patterns`, `shares_modules`

## 📊 New Analysis Capabilities

### 1. Event Prefix Analysis
```python
# Get comprehensive event prefix analysis
analysis = knowledge_graph.get_event_prefix_analysis()

# Results include:
# - Most common prefixes across counties
# - County prefix diversity scores
# - Specialized counties with unique patterns
```

### 2. Module-Application Matrix
```python
# Get module vs application type coverage matrix
matrix = knowledge_graph.get_module_application_matrix()

# Results include:
# - Which counties handle which module-app combinations
# - Coverage gaps and opportunities
# - County specialization patterns
```

### 3. Enhanced County Expertise
```python
# Get expertise scores with naming convention context
expertise = knowledge_graph.get_county_expertise("permits")

# Now considers:
# - Event prefix complexity (WTUA > ASA)
# - Module specificity (non-wildcard > wildcard)
# - Application type coverage
```

## 🔍 Example Parsed Scripts

### Example 1: Asheville Building Permit
**Filename**: `ASA;Permits!Building!New!Application.js`
- **Event Prefix**: ASA (Application Submittal After)
- **Module**: Permits
- **Application Type**: Building
- **Sub Type**: New
- **Category**: Application

### Example 2: Santa Barbara License Workflow
**Filename**: `WTUA;Licenses!Business!~!~.js`
- **Event Prefix**: WTUA (Workflow Task Update After)
- **Module**: Licenses
- **Application Type**: Business
- **Sub Type**: ~ (wildcard)
- **Category**: ~ (wildcard)
- **Is Wildcard**: True

## 🚀 API Endpoints for Naming Convention

### Event Prefix Analysis
```bash
GET /accela/event-prefix-analysis
```
Returns:
- Most common event prefixes
- County prefix diversity
- Specialized counties
- Usage recommendations

### Module Matrix
```bash
GET /accela/module-matrix
```
Returns:
- Module vs application type matrix
- Coverage gaps and opportunities
- County specialization insights

### Naming Insights
```bash
GET /accela/naming-insights?county=asheville
```
Returns:
- Naming convention coverage percentage
- Wildcard usage analysis
- County-specific naming patterns

## 🎯 Enhanced Use Cases

### 1. Find Best Event Handler
```python
# Query: "How to handle permit application submission"
# System now understands:
# - Look for ASA (Application Submittal After) prefixes
# - Focus on Permits module
# - Consider Building application type
# - Prefer non-wildcard implementations
```

### 2. Cross-County Event Pattern Analysis
```python
# Query: "Compare workflow handling across counties"
# System analyzes:
# - WTUA/WTUB prefix usage patterns
# - Module coverage for workflow events
# - County expertise in workflow automation
```

### 3. Implementation Gap Analysis
```python
# Query: "Which counties lack inspection automation?"
# System identifies:
# - Counties missing ISA/IRSA/IFA prefixes
# - Inspection module coverage gaps
# - Opportunities for knowledge transfer
```

## 📈 Benefits of Naming Convention Integration

### 1. **Context-Aware Recommendations**
- Agents understand the specific Accela event context
- Recommendations consider event timing (Before vs After)
- Module and application type matching is precise

### 2. **Enhanced Similarity Calculations**
- Counties are compared based on event prefix patterns
- Module coverage similarity is weighted appropriately
- Wildcard vs specific implementations are differentiated

### 3. **Intelligent Expertise Scoring**
- Complex event prefixes (WTUA) score higher than simple ones (ASA)
- Non-wildcard implementations indicate higher specificity
- Module diversity contributes to expertise scores

### 4. **Pattern Recognition**
- Identify counties with similar event handling patterns
- Discover module-application type combinations
- Find standardization opportunities

## 🔧 Implementation Details

### Parsing Logic
```python
def parse_accela_naming_convention(filename):
    # Parse: ASA;Permits!Building!New!Application.js
    # Returns: AccelaNamingConvention object with:
    #   - event_prefix: "ASA"
    #   - module: "Permits"
    #   - application_type: "Building"
    #   - sub_type: "New"
    #   - category: "Application"
    #   - is_wildcard: False
```

### Graph Enhancement
```python
# Each script node now includes:
script_node = {
    'event_prefix': 'ASA',
    'module': 'Permits',
    'application_type': 'Building',
    'is_wildcard': False,
    # ... other metadata
}

# New relationship types:
graph.add_edge(script, event_prefix_node, relationship="uses_event_prefix")
graph.add_edge(script, module_node, relationship="targets_module")
```

## 🎉 Result

The system now provides **Accela-native intelligence** that understands:
- **When** events trigger (Before/After)
- **What** modules they target
- **Which** application types they handle
- **How** specific vs generic they are

This creates much more accurate recommendations and enables true Accela domain expertise in the agentic system!
