# Development Environment Configuration

# Core configuration
ACCELA_SRC_DIRECTORY=src
ACCELA_METADATA_FILE=accela_metadata.json
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf

# API configuration - Development
ACCELA_API_HOST=127.0.0.1
ACCELA_API_PORT=8001
ACCELA_API_WORKERS=1
ACCELA_API_SECRET_KEY=dev-secret-key-not-for-production
ACCELA_API_CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# LLM configuration (optional)
# OPENAI_API_KEY=your-dev-api-key-here
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300
ACCELA_LLM_MAX_TOKENS_REASONING=150
ACCELA_LLM_TEMPERATURE=0.1

# Agent configuration - Relaxed for development
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=1
ACCELA_SIMILARITY_THRESHOLD=0.2
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.3

# Performance settings - Lower limits for development
ACCELA_MAX_CODE_LENGTH_FOR_LLM=1500
ACCELA_MAX_SEARCH_RESULTS=10
ACCELA_CACHE_ENABLED=false

# Logging - Verbose for development
ACCELA_LOG_LEVEL=DEBUG
ACCELA_LOG_FILE=logs/accela_kb_dev.log

# Development settings
ACCELA_DEBUG=true
ACCELA_RELOAD=true
ACCELA_TEST_MODE=false
