# Production Environment Configuration

# Core configuration
ACCELA_SRC_DIRECTORY=src
ACCELA_METADATA_FILE=accela_metadata.json
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf

# API configuration - Production
ACCELA_API_HOST=0.0.0.0
ACCELA_API_PORT=8001
ACCELA_API_WORKERS=4
ACCELA_API_SECRET_KEY=CHANGE-THIS-IN-PRODUCTION-USE-STRONG-SECRET
ACCELA_API_CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# LLM configuration (set your production API key)
# OPENAI_API_KEY=your-production-api-key-here
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300
ACCELA_LLM_MAX_TOKENS_REASONING=150
ACCELA_LLM_TEMPERATURE=0.1

# Agent configuration - Optimized for production
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=2
ACCELA_SIMILARITY_THRESHOLD=0.3
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.4

# Performance settings - Optimized for production
ACCELA_MAX_CODE_LENGTH_FOR_LLM=2000
ACCELA_MAX_SEARCH_RESULTS=20
ACCELA_CACHE_ENABLED=true

# Logging - Production level
ACCELA_LOG_LEVEL=INFO
ACCELA_LOG_FILE=logs/accela_kb_prod.log

# Production settings
ACCELA_DEBUG=false
ACCELA_RELOAD=false
ACCELA_TEST_MODE=false

# Production monitoring (uncomment when implementing)
# ACCELA_METRICS_ENABLED=true
# ACCELA_HEALTH_CHECK_INTERVAL=30
# ACCELA_PROMETHEUS_PORT=9090
