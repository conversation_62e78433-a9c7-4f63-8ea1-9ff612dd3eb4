#!/usr/bin/env python3
"""
Agentic LLM Integration with Graph Knowledge for Accela Knowledge Base
Demonstrates intelligent agents that can reason about relationships between counties,
functions, and use cases using graph structures for optimal recommendations.
"""

import requests
import json
from typing import List, Dict, Any, Optional, Tuple
import networkx as nx
from dataclasses import dataclass, field
from enum import Enum
import openai  # Optional: for OpenAI integration
from collections import defaultdict
import numpy as np

class AccelaKnowledgeBaseClient:
    """Client for interacting with Accela Knowledge Base API"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        
    def semantic_search(self, query: str, k: int = 5, filters: Dict[str, Any] = None) -> List[Dict]:
        """Perform semantic search"""
        url = f"{self.base_url}/search/semantic"
        payload = {
            "query": query,
            "k": k,
            "filters": filters or {}
        }
        
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()["results"]
    
    def function_search(self, function: str, county: str = None, script_type: str = None, k: int = 5) -> List[Dict]:
        """Search for specific functions"""
        url = f"{self.base_url}/search/code"
        params = {"function": function, "k": k}
        if county:
            params["county"] = county
        if script_type:
            params["script_type"] = script_type
            
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()["results"]
    
    def compare_counties(self, counties: List[str], script_type: str = None, 
                        module: str = None, search_term: str = None) -> Dict:
        """Compare implementations across counties"""
        url = f"{self.base_url}/compare/counties"
        payload = {
            "counties": counties,
            "script_type": script_type,
            "module": module,
            "search_term": search_term
        }
        
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    def get_common_patterns(self, module: str = None, script_type: str = None, min_counties: int = 2) -> Dict:
        """Find common patterns across counties"""
        url = f"{self.base_url}/patterns/common"
        params = {"min_counties": min_counties}
        if module:
            params["module"] = module
        if script_type:
            params["script_type"] = script_type
            
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()

class AccelaLLMAssistant:
    """LLM Assistant with Accela Knowledge Base integration"""
    
    def __init__(self, kb_client: AccelaKnowledgeBaseClient, openai_api_key: str = None):
        self.kb = kb_client
        if openai_api_key:
            openai.api_key = openai_api_key
    
    def answer_question(self, question: str, county: str = None) -> str:
        """Answer questions using knowledge base context"""
        
        # Search for relevant context
        filters = {"county": county} if county else None
        search_results = self.kb.semantic_search(question, k=3, filters=filters)
        
        # Build context from search results
        context_parts = []
        for result in search_results:
            metadata = result["metadata"]
            context_parts.append(f"""
County: {metadata['county']}
Script Type: {metadata['script_type']}
File: {metadata['file_path']}
Content: {result['content'][:500]}...
""")
        
        context = "\n---\n".join(context_parts)
        
        # Create prompt for LLM
        prompt = f"""
You are an expert Accela developer assistant. Use the following context from actual Accela implementations to answer the user's question.

Context from Accela Knowledge Base:
{context}

User Question: {question}

Please provide a detailed answer based on the context above. Include specific examples from the code when relevant.
If the context doesn't contain enough information, say so and suggest what additional information might be needed.
"""
        
        # For demonstration, return the prompt (in real usage, send to LLM)
        return prompt
    
    def suggest_implementation(self, requirement: str, target_county: str = None) -> str:
        """Suggest implementation approach based on existing patterns"""
        
        # Search for similar implementations
        search_results = self.kb.semantic_search(requirement, k=5)
        
        # Find common patterns
        common_patterns = self.kb.get_common_patterns(min_counties=2)
        
        # Build suggestion
        suggestion_parts = [
            f"Implementation suggestion for: {requirement}",
            "\n## Similar Existing Implementations:"
        ]
        
        for i, result in enumerate(search_results, 1):
            metadata = result["metadata"]
            suggestion_parts.append(f"""
{i}. {metadata['county']} - {metadata['script_type']}
   File: {metadata['file_path']}
   Functions: {', '.join(metadata.get('functions', [])[:3])}
   Relevance Score: {result['score']:.3f}
""")
        
        suggestion_parts.append("\n## Common Functions You Might Need:")
        for func, info in list(common_patterns["common_functions"].items())[:5]:
            suggestion_parts.append(f"- {func} (used by {info['county_count']} counties)")
        
        return "\n".join(suggestion_parts)
    
    def code_review_assistant(self, code: str, script_type: str = "event") -> str:
        """Provide code review suggestions based on best practices"""
        
        # Search for similar code patterns
        search_results = self.kb.semantic_search(f"{script_type} script best practices", k=3)
        
        # Get common patterns for this script type
        patterns = self.kb.get_common_patterns(script_type=script_type)
        
        review_parts = [
            "## Code Review Suggestions",
            f"\nAnalyzing {script_type} script...",
            "\n### Similar Implementations Found:"
        ]
        
        for result in search_results:
            metadata = result["metadata"]
            review_parts.append(f"- {metadata['county']}: {metadata['file_path']}")
        
        review_parts.append(f"\n### Common Functions in {script_type} scripts:")
        for func in list(patterns["common_functions"].keys())[:5]:
            review_parts.append(f"- {func}")
        
        review_parts.append("\n### Recommendations:")
        review_parts.append("- Compare your implementation with the similar scripts above")
        review_parts.append("- Consider using common functions for consistency")
        review_parts.append("- Follow naming conventions used across counties")
        
        return "\n".join(review_parts)

def demo_llm_integration():
    """Demonstrate LLM integration capabilities"""
    
    print("🤖 Accela LLM Integration Demo")
    print("=" * 50)
    
    # Initialize clients
    kb_client = AccelaKnowledgeBaseClient()
    assistant = AccelaLLMAssistant(kb_client)
    
    # Demo 1: Question Answering
    print("\n1. Question Answering Demo")
    print("-" * 30)
    
    question = "How do I send email notifications when a permit is issued?"
    print(f"Question: {question}")
    
    try:
        answer_prompt = assistant.answer_question(question)
        print("\nGenerated LLM Prompt:")
        print(answer_prompt[:500] + "..." if len(answer_prompt) > 500 else answer_prompt)
    except Exception as e:
        print(f"Error: {e}")
    
    # Demo 2: Implementation Suggestions
    print("\n\n2. Implementation Suggestion Demo")
    print("-" * 40)
    
    requirement = "Automatically schedule inspections when permit is ready"
    print(f"Requirement: {requirement}")
    
    try:
        suggestion = assistant.suggest_implementation(requirement)
        print("\nSuggestion:")
        print(suggestion)
    except Exception as e:
        print(f"Error: {e}")
    
    # Demo 3: Code Review
    print("\n\n3. Code Review Demo")
    print("-" * 25)
    
    sample_code = """
    function onPermitIssued() {
        var emailAddr = "<EMAIL>";
        aa.sendMail(emailAddr, "Permit Issued", "Your permit is ready");
    }
    """
    
    print(f"Sample Code: {sample_code}")
    
    try:
        review = assistant.code_review_assistant(sample_code, "event")
        print("\nCode Review:")
        print(review)
    except Exception as e:
        print(f"Error: {e}")

def example_chatbot_integration():
    """Example of how to integrate with a chatbot"""
    
    class AccelaChatbot:
        def __init__(self):
            self.kb_client = AccelaKnowledgeBaseClient()
            self.assistant = AccelaLLMAssistant(self.kb_client)
        
        def process_message(self, message: str, context: Dict = None) -> str:
            """Process user message and return response"""
            
            # Determine intent
            if "how to" in message.lower() or "how do i" in message.lower():
                return self.assistant.answer_question(message)
            
            elif "compare" in message.lower():
                # Extract counties from message (simplified)
                counties = ["asheville", "santa_barbara"]  # In real implementation, parse from message
                comparison = self.kb_client.compare_counties(counties)
                return f"Comparison results: {json.dumps(comparison, indent=2)}"
            
            elif "function" in message.lower():
                # Extract function name (simplified)
                words = message.split()
                func_idx = words.index("function") + 1
                if func_idx < len(words):
                    function_name = words[func_idx]
                    results = self.kb_client.function_search(function_name)
                    return f"Found {len(results)} implementations of {function_name}"
            
            else:
                # Default semantic search
                results = self.kb_client.semantic_search(message, k=3)
                return f"Found {len(results)} relevant scripts"
    
    # Demo the chatbot
    chatbot = AccelaChatbot()
    
    test_messages = [
        "How do I send email notifications?",
        "Compare permit workflows between counties",
        "Show me the emailContact function",
        "What are common inspection scripts?"
    ]
    
    print("\n🤖 Chatbot Integration Demo")
    print("=" * 40)
    
    for message in test_messages:
        print(f"\nUser: {message}")
        try:
            response = chatbot.process_message(message)
            print(f"Bot: {response[:200]}..." if len(response) > 200 else f"Bot: {response}")
        except Exception as e:
            print(f"Bot: Sorry, I encountered an error: {e}")

if __name__ == "__main__":
    print("Starting LLM Integration Examples...")
    print("Make sure the knowledge base API is running on localhost:8000")
    
    try:
        demo_llm_integration()
        example_chatbot_integration()
    except requests.exceptions.ConnectionError:
        print("\n❌ Could not connect to knowledge base API.")
        print("Please start the API server first:")
        print("python knowledge_base_api.py")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure all dependencies are installed and the API is running.")
