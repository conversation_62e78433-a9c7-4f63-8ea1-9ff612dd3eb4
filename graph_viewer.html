
<!DOCTYPE html>
<html>
<head>
    <title>Accela Knowledge Graph Viewer</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        #graph { border: 1px solid #ccc; }
        .controls { margin-bottom: 20px; }
        .node { fill: #69b3a2; stroke: #fff; stroke-width: 2px; }
        .node.county { fill: #ff6b6b; }
        .node.script { fill: #4ecdc4; }
        .node.function { fill: #45b7d1; }
        .node.event_prefix { fill: #96ceb4; }
        .link { stroke: #999; stroke-opacity: 0.6; stroke-width: 1px; }
        .tooltip { position: absolute; padding: 10px; background: rgba(0,0,0,0.8); color: white; border-radius: 5px; pointer-events: none; }
    </style>
</head>
<body>
    <h1>🎯 Accela Knowledge Graph Visualization</h1>
    
    <div class="controls">
        <button onclick="zoomToFit()">Zoom to Fit</button>
        <button onclick="resetZoom()">Reset Zoom</button>
        <label>Filter by type: 
            <select id="nodeTypeFilter" onchange="filterNodes()">
                <option value="">All</option>
                <option value="county">Counties</option>
                <option value="script">Scripts</option>
                <option value="function">Functions</option>
                <option value="event_prefix">Event Prefixes</option>
            </select>
        </label>
    </div>
    
    <svg id="graph" width="1200" height="800"></svg>
    
    <script>
        // Load and visualize the graph
        d3.json("accela_knowledge_graph.json").then(function(data) {
            const svg = d3.select("#graph");
            const width = +svg.attr("width");
            const height = +svg.attr("height");
            
            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);
            
            // Create zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([0.1, 10])
                .on("zoom", function(event) {
                    container.attr("transform", event.transform);
                });
            
            svg.call(zoom);
            
            const container = svg.append("g");
            
            // Create force simulation
            const simulation = d3.forceSimulation(data.nodes)
                .force("link", d3.forceLink(data.edges).id(d => d.id).distance(100))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2));
            
            // Create links
            const link = container.append("g")
                .selectAll("line")
                .data(data.edges)
                .enter().append("line")
                .attr("class", "link");
            
            // Create nodes
            const node = container.append("g")
                .selectAll("circle")
                .data(data.nodes)
                .enter().append("circle")
                .attr("class", d => `node ${d.type || 'default'}`)
                .attr("r", d => {
                    if (d.type === 'county') return 15;
                    if (d.type === 'script') return 8;
                    if (d.type === 'function') return 5;
                    return 6;
                })
                .on("mouseover", function(event, d) {
                    tooltip.transition().duration(200).style("opacity", .9);
                    tooltip.html(`
                        <strong>${d.id}</strong><br/>
                        Type: ${d.type || 'unknown'}<br/>
                        ${d.county ? `County: ${d.county}<br/>` : ''}
                        ${d.functions ? `Functions: ${d.functions.split(',').length}<br/>` : ''}
                    `)
                    .style("left", (event.pageX + 10) + "px")
                    .style("top", (event.pageY - 28) + "px");
                })
                .on("mouseout", function(d) {
                    tooltip.transition().duration(500).style("opacity", 0);
                })
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended));
            
            // Add labels for important nodes
            const label = container.append("g")
                .selectAll("text")
                .data(data.nodes.filter(d => d.type === 'county'))
                .enter().append("text")
                .text(d => d.id.replace('county:', ''))
                .style("font-size", "12px")
                .style("text-anchor", "middle");
            
            // Update positions on simulation tick
            simulation.on("tick", function() {
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                label
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + 25);
            });
            
            // Drag functions
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }
            
            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }
            
            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
            
            // Global functions
            window.zoomToFit = function() {
                const bounds = container.node().getBBox();
                const fullWidth = width;
                const fullHeight = height;
                const widthScale = fullWidth / bounds.width;
                const heightScale = fullHeight / bounds.height;
                const scale = 0.8 * Math.min(widthScale, heightScale);
                const translate = [fullWidth / 2 - scale * (bounds.x + bounds.width / 2), fullHeight / 2 - scale * (bounds.y + bounds.height / 2)];
                
                svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
            };
            
            window.resetZoom = function() {
                svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity);
            };
            
            window.filterNodes = function() {
                const filterType = document.getElementById('nodeTypeFilter').value;
                node.style('opacity', d => filterType === '' || d.type === filterType ? 1 : 0.1);
                link.style('opacity', d => {
                    if (filterType === '') return 0.6;
                    return (d.source.type === filterType || d.target.type === filterType) ? 0.6 : 0.1;
                });
            };
        });
    </script>
</body>
</html>
