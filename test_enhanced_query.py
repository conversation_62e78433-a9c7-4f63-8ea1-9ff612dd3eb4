#!/usr/bin/env python3
"""
Test the enhanced natural language query system
"""

import requests
import json
from pathlib import Path


def test_enhanced_query():
    """Test enhanced query with code analysis"""
    
    print("🧪 Testing Enhanced Natural Language Query System")
    print("=" * 60)
    
    # Test query
    query = "How does Asheville handle building permit email notifications?"
    
    print(f"🎯 Query: {query}")
    print("📡 Sending request...")
    
    try:
        response = requests.post('http://localhost:8001/agentic/ask', 
            json={'query': query},
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Success!")
            
            # Save the HTML response
            filename = "enhanced_asheville_analysis.html"
            with open(filename, 'w') as f:
                f.write(response.text)
            
            print(f"📄 Enhanced analysis saved to: {filename}")
            print(f"🌐 Open in browser: file://{Path(filename).absolute()}")
            
            # Check content length
            content_length = len(response.text)
            print(f"📊 Response size: {content_length:,} characters")
            
            # Check for key sections
            content = response.text.lower()
            sections_found = []
            
            if "email implementation details" in content:
                sections_found.append("📧 Email Implementation Details")
            if "key code snippets" in content:
                sections_found.append("🔧 Key Code Snippets")
            if "code patterns" in content:
                sections_found.append("📋 Code Patterns")
            if "recommended implementation" in content:
                sections_found.append("🏆 Recommended Implementation")
            
            if sections_found:
                print("✅ Enhanced sections found:")
                for section in sections_found:
                    print(f"   {section}")
            else:
                print("⚠️ Basic response - enhanced sections not detected")
            
            return True
            
        else:
            print(f"❌ Error {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_comparison_query():
    """Test comparison query"""
    
    print("\n" + "=" * 60)
    print("🔄 Testing Comparison Query")
    
    query = "Compare Asheville and Santa Barbara building permit email notifications"
    
    print(f"🎯 Query: {query}")
    print("📡 Sending request...")
    
    try:
        response = requests.post('http://localhost:8001/agentic/ask', 
            json={
                'query': query,
                'counties': 'asheville, santa_barbara'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ Success!")
            
            # Save the HTML response
            filename = "comparison_asheville_vs_santa_barbara.html"
            with open(filename, 'w') as f:
                f.write(response.text)
            
            print(f"📄 Comparison analysis saved to: {filename}")
            print(f"🌐 Open in browser: file://{Path(filename).absolute()}")
            
            # Check for comparison sections
            content = response.text.lower()
            comparison_sections = []
            
            if "implementation comparison" in content:
                comparison_sections.append("📊 Implementation Comparison")
            if "vs" in content or "versus" in content:
                comparison_sections.append("🆚 County Comparison")
            if "differences" in content:
                comparison_sections.append("🔍 Differences Analysis")
            if "unique features" in content:
                comparison_sections.append("🌟 Unique Features")
            
            if comparison_sections:
                print("✅ Comparison sections found:")
                for section in comparison_sections:
                    print(f"   {section}")
            
            return True
            
        else:
            print(f"❌ Error {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_json_endpoint():
    """Test JSON endpoint with markdown"""
    
    print("\n" + "=" * 60)
    print("📊 Testing JSON Endpoint with Markdown")
    
    query = "Show me fee calculation implementations with actual code"
    
    print(f"🎯 Query: {query}")
    print("📡 Sending request...")
    
    try:
        response = requests.post('http://localhost:8001/agentic/query', 
            json={
                'query': query,
                'output_format': 'markdown'
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"📊 Confidence: {result['confidence']:.1%}")
            print(f"⏱️ Processing Time: {result['processing_time']:.2f}s")
            
            if result.get('markdown_output'):
                # Save markdown
                filename = "fee_calculation_analysis.md"
                with open(filename, 'w') as f:
                    f.write(result['markdown_output'])
                
                print(f"📝 Markdown saved to: {filename}")
                print(f"📄 Markdown size: {len(result['markdown_output']):,} characters")
            
            return True
            
        else:
            print(f"❌ Error {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def main():
    """Run all tests"""
    
    print("🚀 Enhanced Natural Language Query System Tests")
    print("=" * 60)
    
    tests = [
        ("Single County Analysis", test_enhanced_query),
        ("County Comparison", test_comparison_query),
        ("JSON with Markdown", test_json_endpoint)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        success = test_func()
        results.append((test_name, success))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Enhanced system is working perfectly!")
        print("\n📁 Generated files:")
        html_files = list(Path('.').glob('*.html'))
        md_files = list(Path('.').glob('*.md'))
        
        for file in html_files:
            print(f"   🌐 {file}")
        for file in md_files:
            print(f"   📝 {file}")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
    
    print(f"\n🔗 API Endpoints:")
    print(f"   • Natural Language: POST http://localhost:8001/agentic/ask")
    print(f"   • JSON Response: POST http://localhost:8001/agentic/query")
    print(f"   • API Documentation: http://localhost:8001/docs")


if __name__ == "__main__":
    main()
