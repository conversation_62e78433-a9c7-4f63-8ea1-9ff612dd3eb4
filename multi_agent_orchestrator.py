#!/usr/bin/env python3
"""
Multi-Agent Orchestrator for Accela Knowledge Base
Coordinates multiple intelligent agents to provide optimal recommendations
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
import asyncio
import json
from datetime import datetime
import uuid

from agentic_graph_system import <PERSON><PERSON><PERSON><PERSON>nowledgeGraph, AgentTask, AgentResponse, AgentRole
from intelligent_agents import AnalyzerAgent, ComparatorAgent, BaseAgent

@dataclass
class OrchestrationRequest:
    """Request for multi-agent orchestration"""
    request_id: str
    query: str
    use_case: str
    target_counties: Optional[List[str]] = None
    constraints: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1

@dataclass
class OrchestrationResult:
    """Result from multi-agent orchestration"""
    request_id: str
    query: str
    best_implementation: Dict[str, Any]
    alternatives: List[Dict[str, Any]]
    synthesis: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str]
    agent_responses: Dict[str, AgentResponse] = field(default_factory=dict)

class RecommenderAgent(BaseAgent):
    """Agent that recommends best implementations based on analysis"""
    
    def __init__(self, knowledge_graph: AccelaKnowledgeGraph):
        super().__init__("BestPracticeRecommender", AgentRole.RECOMMENDER, knowledge_graph)
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Recommend best implementations"""
        
        query = task.query
        context = task.context
        
        # Get analysis from other agents
        analyzer_result = context.get('analyzer_result', {})
        comparator_result = context.get('comparator_result', {})
        
        # Find best implementations using graph analysis
        best_implementations = self.knowledge_graph.find_best_implementations(
            query, 
            criteria={
                'complexity': 0.3,
                'doc_quality': 0.4,
                'usage_frequency': 0.3
            }
        )
        
        # Enhance recommendations with agent insights
        enhanced_recommendations = self._enhance_with_agent_insights(
            best_implementations, analyzer_result, comparator_result
        )
        
        # Generate final recommendations
        final_recommendations = self._generate_final_recommendations(
            enhanced_recommendations, query
        )
        
        reasoning = self._generate_recommendation_reasoning(
            enhanced_recommendations, analyzer_result, comparator_result
        )
        
        return AgentResponse(
            task_id=task.task_id,
            agent_role=self.role,
            result={
                'best_implementations': enhanced_recommendations[:5],
                'total_found': len(enhanced_recommendations),
                'recommendation_criteria': {
                    'complexity': 0.3,
                    'doc_quality': 0.4,
                    'usage_frequency': 0.3
                }
            },
            confidence=0.9,
            reasoning=reasoning,
            recommendations=final_recommendations
        )
    
    def _enhance_with_agent_insights(self, implementations: List[Dict], 
                                   analyzer_result: Dict, comparator_result: Dict) -> List[Dict]:
        """Enhance implementations with insights from other agents"""
        
        enhanced = []
        
        # Get best practices from comparator
        best_practices = comparator_result.get('best_practices', [])
        best_practice_counties = {bp.get('county') for bp in best_practices if bp.get('county')}
        
        # Get cross-county functions from analyzer
        cross_county_functions = analyzer_result.get('function_usage', {}).get('cross_county_functions', [])
        
        for impl in implementations:
            enhanced_impl = impl.copy()
            
            # Boost score if from best practice county
            if impl['county'] in best_practice_counties:
                enhanced_impl['score'] += 0.2
                enhanced_impl['boost_reason'] = "From best practice county"
            
            # Boost score if uses cross-county functions
            impl_functions = impl['metadata'].get('functions', [])
            common_func_count = len(set(impl_functions) & set(cross_county_functions))
            if common_func_count > 0:
                enhanced_impl['score'] += 0.1 * common_func_count
                enhanced_impl['common_functions'] = common_func_count
            
            enhanced.append(enhanced_impl)
        
        # Re-sort by enhanced score
        enhanced.sort(key=lambda x: x['score'], reverse=True)
        
        return enhanced
    
    def _generate_final_recommendations(self, implementations: List[Dict], query: str) -> List[str]:
        """Generate final actionable recommendations"""
        
        recommendations = []
        
        if implementations:
            best = implementations[0]
            recommendations.append(f"Primary recommendation: Use {best['county']}'s implementation")
            recommendations.append(f"Rationale: {best['reasoning']}")
            
            if len(implementations) > 1:
                alternatives = [impl['county'] for impl in implementations[1:3]]
                recommendations.append(f"Alternative approaches: {', '.join(alternatives)}")
        
        # Add specific function recommendations
        all_functions = set()
        for impl in implementations[:3]:
            all_functions.update(impl['metadata'].get('functions', []))
        
        if all_functions:
            top_functions = list(all_functions)[:5]
            recommendations.append(f"Key functions to implement: {', '.join(top_functions)}")
        
        return recommendations
    
    def _generate_recommendation_reasoning(self, implementations: List[Dict], 
                                         analyzer_result: Dict, comparator_result: Dict) -> str:
        """Generate reasoning for recommendations"""
        
        reasoning_parts = []
        
        if implementations:
            best = implementations[0]
            reasoning_parts.append(f"Top choice: {best['county']} (score: {best['score']:.2f})")
            
            if best.get('boost_reason'):
                reasoning_parts.append(f"Boosted: {best['boost_reason']}")
        
        # Add analyzer insights
        if analyzer_result:
            complexity = analyzer_result.get('complexity_analysis', {})
            reasoning_parts.append(f"Complexity: {complexity.get('recommendation', 'Unknown')}")
        
        # Add comparator insights
        if comparator_result:
            best_practices = comparator_result.get('best_practices', [])
            if best_practices:
                reasoning_parts.append(f"Best practices from: {best_practices[0].get('county', 'Unknown')}")
        
        return " | ".join(reasoning_parts)

class SynthesizerAgent(BaseAgent):
    """Agent that synthesizes solutions from multiple sources"""
    
    def __init__(self, knowledge_graph: AccelaKnowledgeGraph):
        super().__init__("SolutionSynthesizer", AgentRole.SYNTHESIZER, knowledge_graph)
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Synthesize optimal solution from multiple implementations"""
        
        context = task.context
        query = task.query
        
        # Get results from other agents
        analyzer_result = context.get('analyzer_result', {})
        comparator_result = context.get('comparator_result', {})
        recommender_result = context.get('recommender_result', {})
        
        # Synthesize solution
        synthesis = self._synthesize_solution(
            query, analyzer_result, comparator_result, recommender_result
        )
        
        reasoning = self._generate_synthesis_reasoning(synthesis)
        
        return AgentResponse(
            task_id=task.task_id,
            agent_role=self.role,
            result=synthesis,
            confidence=0.85,
            reasoning=reasoning,
            recommendations=self._generate_synthesis_recommendations(synthesis)
        )
    
    def _synthesize_solution(self, query: str, analyzer_result: Dict, 
                           comparator_result: Dict, recommender_result: Dict) -> Dict[str, Any]:
        """Synthesize optimal solution"""
        
        synthesis = {
            'optimal_approach': {},
            'hybrid_solution': {},
            'implementation_plan': [],
            'risk_assessment': {},
            'success_metrics': []
        }
        
        # Get best implementations
        best_implementations = recommender_result.get('best_implementations', [])
        
        if best_implementations:
            primary = best_implementations[0]
            
            # Create optimal approach
            synthesis['optimal_approach'] = {
                'primary_county': primary['county'],
                'primary_score': primary['score'],
                'key_functions': primary['metadata'].get('functions', []),
                'complexity': primary['metadata'].get('complexity', 'unknown'),
                'documentation': primary['metadata'].get('doc_quality', 'unknown')
            }
            
            # Create hybrid solution if multiple good options
            if len(best_implementations) > 1:
                synthesis['hybrid_solution'] = self._create_hybrid_solution(best_implementations)
            
            # Create implementation plan
            synthesis['implementation_plan'] = self._create_implementation_plan(
                primary, analyzer_result, comparator_result
            )
            
            # Risk assessment
            synthesis['risk_assessment'] = self._assess_risks(
                primary, analyzer_result, comparator_result
            )
            
            # Success metrics
            synthesis['success_metrics'] = self._define_success_metrics(primary)
        
        return synthesis
    
    def _create_hybrid_solution(self, implementations: List[Dict]) -> Dict[str, Any]:
        """Create hybrid solution from multiple implementations"""
        
        hybrid = {
            'approach': 'hybrid',
            'components': [],
            'rationale': 'Combine best aspects from multiple counties'
        }
        
        for impl in implementations[:3]:  # Top 3 implementations
            component = {
                'county': impl['county'],
                'contribution': 'primary' if impl == implementations[0] else 'supplementary',
                'key_functions': impl['metadata'].get('functions', [])[:3],
                'strength': impl.get('boost_reason', 'High quality implementation')
            }
            hybrid['components'].append(component)
        
        return hybrid
    
    def _create_implementation_plan(self, primary_impl: Dict, 
                                  analyzer_result: Dict, comparator_result: Dict) -> List[Dict[str, Any]]:
        """Create step-by-step implementation plan"""
        
        plan = []
        
        # Phase 1: Analysis and Planning
        plan.append({
            'phase': 1,
            'title': 'Analysis and Planning',
            'tasks': [
                'Review primary implementation from ' + primary_impl['county'],
                'Analyze function dependencies',
                'Plan integration approach'
            ],
            'duration': '1-2 weeks'
        })
        
        # Phase 2: Core Implementation
        core_functions = primary_impl['metadata'].get('functions', [])[:5]
        plan.append({
            'phase': 2,
            'title': 'Core Implementation',
            'tasks': [f'Implement {func}' for func in core_functions],
            'duration': '2-3 weeks'
        })
        
        # Phase 3: Testing and Validation
        plan.append({
            'phase': 3,
            'title': 'Testing and Validation',
            'tasks': [
                'Unit testing',
                'Integration testing',
                'Performance validation'
            ],
            'duration': '1 week'
        })
        
        return plan
    
    def _assess_risks(self, primary_impl: Dict, analyzer_result: Dict, 
                     comparator_result: Dict) -> Dict[str, Any]:
        """Assess implementation risks"""
        
        risks = {
            'complexity_risk': 'low',
            'integration_risk': 'medium',
            'maintenance_risk': 'low',
            'mitigation_strategies': []
        }
        
        # Assess complexity risk
        complexity = primary_impl['metadata'].get('complexity', 'medium')
        if complexity == 'high':
            risks['complexity_risk'] = 'high'
            risks['mitigation_strategies'].append('Break down into smaller components')
        
        # Assess documentation risk
        doc_quality = primary_impl['metadata'].get('doc_quality', 'poor')
        if doc_quality == 'poor':
            risks['maintenance_risk'] = 'high'
            risks['mitigation_strategies'].append('Improve documentation during implementation')
        
        return risks
    
    def _define_success_metrics(self, primary_impl: Dict) -> List[str]:
        """Define success metrics for implementation"""
        
        metrics = [
            'Implementation completed within timeline',
            'All core functions working correctly',
            'Performance meets requirements'
        ]
        
        # Add specific metrics based on implementation
        functions = primary_impl['metadata'].get('functions', [])
        if 'email' in str(functions).lower():
            metrics.append('Email notifications sent successfully')
        
        if 'validation' in str(functions).lower():
            metrics.append('Data validation accuracy > 95%')
        
        return metrics
    
    def _generate_synthesis_reasoning(self, synthesis: Dict[str, Any]) -> str:
        """Generate reasoning for synthesis"""
        
        reasoning_parts = []
        
        optimal = synthesis.get('optimal_approach', {})
        if optimal:
            reasoning_parts.append(f"Primary: {optimal.get('primary_county')} (score: {optimal.get('primary_score', 0):.2f})")
        
        hybrid = synthesis.get('hybrid_solution', {})
        if hybrid:
            reasoning_parts.append(f"Hybrid approach with {len(hybrid.get('components', []))} components")
        
        plan = synthesis.get('implementation_plan', [])
        reasoning_parts.append(f"Implementation plan: {len(plan)} phases")
        
        return " | ".join(reasoning_parts)
    
    def _generate_synthesis_recommendations(self, synthesis: Dict[str, Any]) -> List[str]:
        """Generate recommendations from synthesis"""
        
        recommendations = []
        
        optimal = synthesis.get('optimal_approach', {})
        if optimal:
            recommendations.append(f"Adopt {optimal.get('primary_county')}'s approach as primary solution")
        
        risks = synthesis.get('risk_assessment', {})
        for strategy in risks.get('mitigation_strategies', []):
            recommendations.append(f"Risk mitigation: {strategy}")
        
        plan = synthesis.get('implementation_plan', [])
        if plan:
            recommendations.append(f"Follow {len(plan)}-phase implementation plan")
        
        return recommendations

class MultiAgentOrchestrator:
    """Orchestrates multiple agents to provide optimal recommendations"""
    
    def __init__(self, knowledge_graph: AccelaKnowledgeGraph):
        self.knowledge_graph = knowledge_graph
        self.agents = {
            AgentRole.ANALYZER: AnalyzerAgent(knowledge_graph),
            AgentRole.COMPARATOR: ComparatorAgent(knowledge_graph),
            AgentRole.RECOMMENDER: RecommenderAgent(knowledge_graph),
            AgentRole.SYNTHESIZER: SynthesizerAgent(knowledge_graph)
        }
        
    async def orchestrate(self, request: OrchestrationRequest) -> OrchestrationResult:
        """Orchestrate multiple agents to handle the request"""
        
        print(f"🤖 Orchestrating agents for: {request.query}")
        
        # Create tasks for each agent
        tasks = self._create_agent_tasks(request)
        
        # Execute agents in sequence (with dependencies)
        agent_responses = {}
        
        # Phase 1: Analysis
        analyzer_task = tasks[AgentRole.ANALYZER]
        analyzer_response = self.agents[AgentRole.ANALYZER].execute_task(analyzer_task)
        agent_responses[AgentRole.ANALYZER] = analyzer_response
        
        # Phase 2: Comparison (uses analyzer results)
        comparator_task = tasks[AgentRole.COMPARATOR]
        comparator_task.context['analyzer_result'] = analyzer_response.result
        comparator_response = self.agents[AgentRole.COMPARATOR].execute_task(comparator_task)
        agent_responses[AgentRole.COMPARATOR] = comparator_response
        
        # Phase 3: Recommendation (uses both previous results)
        recommender_task = tasks[AgentRole.RECOMMENDER]
        recommender_task.context.update({
            'analyzer_result': analyzer_response.result,
            'comparator_result': comparator_response.result
        })
        recommender_response = self.agents[AgentRole.RECOMMENDER].execute_task(recommender_task)
        agent_responses[AgentRole.RECOMMENDER] = recommender_response
        
        # Phase 4: Synthesis (uses all previous results)
        synthesizer_task = tasks[AgentRole.SYNTHESIZER]
        synthesizer_task.context.update({
            'analyzer_result': analyzer_response.result,
            'comparator_result': comparator_response.result,
            'recommender_result': recommender_response.result
        })
        synthesizer_response = self.agents[AgentRole.SYNTHESIZER].execute_task(synthesizer_task)
        agent_responses[AgentRole.SYNTHESIZER] = synthesizer_response
        
        # Compile final result
        result = self._compile_final_result(request, agent_responses)
        
        print(f"✅ Orchestration complete. Confidence: {result.confidence:.2f}")
        
        return result
    
    def _create_agent_tasks(self, request: OrchestrationRequest) -> Dict[AgentRole, AgentTask]:
        """Create tasks for each agent"""
        
        base_context = {
            'use_case': request.use_case,
            'target_counties': request.target_counties,
            'constraints': request.constraints
        }
        
        tasks = {}
        
        # Analyzer task
        tasks[AgentRole.ANALYZER] = AgentTask(
            task_id=f"{request.request_id}_analyzer",
            agent_role=AgentRole.ANALYZER,
            query=request.query,
            context=base_context.copy(),
            priority=request.priority
        )
        
        # Comparator task
        tasks[AgentRole.COMPARATOR] = AgentTask(
            task_id=f"{request.request_id}_comparator",
            agent_role=AgentRole.COMPARATOR,
            query=request.query,
            context=base_context.copy(),
            priority=request.priority
        )
        
        # Recommender task
        tasks[AgentRole.RECOMMENDER] = AgentTask(
            task_id=f"{request.request_id}_recommender",
            agent_role=AgentRole.RECOMMENDER,
            query=request.query,
            context=base_context.copy(),
            priority=request.priority
        )
        
        # Synthesizer task
        tasks[AgentRole.SYNTHESIZER] = AgentTask(
            task_id=f"{request.request_id}_synthesizer",
            agent_role=AgentRole.SYNTHESIZER,
            query=request.query,
            context=base_context.copy(),
            priority=request.priority
        )
        
        return tasks
    
    def _compile_final_result(self, request: OrchestrationRequest, 
                            agent_responses: Dict[AgentRole, AgentResponse]) -> OrchestrationResult:
        """Compile final orchestration result"""
        
        # Get best implementation from recommender
        recommender_result = agent_responses[AgentRole.RECOMMENDER].result
        best_implementations = recommender_result.get('best_implementations', [])
        
        best_implementation = best_implementations[0] if best_implementations else {}
        alternatives = best_implementations[1:4] if len(best_implementations) > 1 else []
        
        # Get synthesis from synthesizer
        synthesis = agent_responses[AgentRole.SYNTHESIZER].result
        
        # Calculate overall confidence
        confidences = [response.confidence for response in agent_responses.values()]
        overall_confidence = sum(confidences) / len(confidences)
        
        # Compile reasoning
        reasoning_parts = []
        for role, response in agent_responses.items():
            reasoning_parts.append(f"{role.value}: {response.reasoning}")
        
        # Compile recommendations
        all_recommendations = []
        for response in agent_responses.values():
            all_recommendations.extend(response.recommendations)
        
        return OrchestrationResult(
            request_id=request.request_id,
            query=request.query,
            best_implementation=best_implementation,
            alternatives=alternatives,
            synthesis=synthesis,
            confidence=overall_confidence,
            reasoning=" | ".join(reasoning_parts),
            recommendations=all_recommendations,
            agent_responses=agent_responses
        )

# Example usage function
async def demo_multi_agent_orchestration():
    """Demonstrate multi-agent orchestration"""
    
    print("🚀 Multi-Agent Orchestration Demo")
    print("=" * 50)
    
    # Initialize system
    knowledge_graph = AccelaKnowledgeGraph()
    
    # Build graph (in real usage, this would be done once)
    try:
        knowledge_graph.build_graph_from_metadata()
    except FileNotFoundError:
        print("❌ Metadata file not found. Please run knowledge_base_setup.py first.")
        return
    
    orchestrator = MultiAgentOrchestrator(knowledge_graph)
    
    # Create test request
    request = OrchestrationRequest(
        request_id=str(uuid.uuid4()),
        query="email notification when permit is issued",
        use_case="permit_notification",
        target_counties=["asheville", "santa_barbara", "dayton"],
        constraints={"complexity": "low", "documentation": "good"}
    )
    
    # Execute orchestration
    result = await orchestrator.orchestrate(request)
    
    # Display results
    print(f"\n📊 Results for: {result.query}")
    print(f"Confidence: {result.confidence:.2f}")
    print(f"\n🏆 Best Implementation:")
    if result.best_implementation:
        print(f"  County: {result.best_implementation.get('county', 'Unknown')}")
        print(f"  Score: {result.best_implementation.get('score', 0):.2f}")
        print(f"  Reasoning: {result.best_implementation.get('reasoning', 'N/A')}")
    
    print(f"\n🔄 Alternatives:")
    for i, alt in enumerate(result.alternatives, 1):
        print(f"  {i}. {alt.get('county', 'Unknown')} (score: {alt.get('score', 0):.2f})")
    
    print(f"\n🧠 Synthesis:")
    optimal = result.synthesis.get('optimal_approach', {})
    if optimal:
        print(f"  Primary: {optimal.get('primary_county', 'Unknown')}")
        print(f"  Complexity: {optimal.get('complexity', 'Unknown')}")
        print(f"  Key Functions: {', '.join(optimal.get('key_functions', [])[:3])}")
    
    print(f"\n📋 Recommendations:")
    for i, rec in enumerate(result.recommendations[:5], 1):
        print(f"  {i}. {rec}")

if __name__ == "__main__":
    asyncio.run(demo_multi_agent_orchestration())
