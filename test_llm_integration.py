#!/usr/bin/env python3
"""
Test script for LLM integration in the Accela Knowledge Base
Verifies that LLM intelligence works correctly when enabled
"""

import os
import sys
from intelligent_agents import LLMHelper

def test_llm_availability():
    """Test if LLM integration is available and working"""
    
    print("🧪 Testing LLM Integration")
    print("=" * 50)
    
    # Check if OpenAI API key is set
    api_key = os.getenv('OPENAI_API_KEY')
    
    if not api_key:
        print("❌ OPENAI_API_KEY not set")
        print("   Set it with: export OPENAI_API_KEY='your-key-here'")
        print("   System will work with rule-based intelligence only")
        return False
    
    print("✅ OPENAI_API_KEY found")
    
    # Test LLMHelper initialization
    try:
        llm_helper = LLMHelper(api_key)
        
        if not llm_helper.available:
            print("❌ LLMHelper not available (check API key or OpenAI installation)")
            return False
        
        print("✅ LLMHelper initialized successfully")
        
    except Exception as e:
        print(f"❌ LLMHelper initialization failed: {e}")
        return False
    
    # Test code semantic analysis
    print("\n🔍 Testing Code Semantic Analysis...")
    
    test_code = """
    function emailContact(contactType, emailTemplate) {
        try {
            var contact = aa.people.getCapContactByCapID(capId).getOutput();
            if (contact && contact.getEmail()) {
                var emailBody = generateEmailBody(emailTemplate, capId);
                aa.sendMail("<EMAIL>", contact.getEmail(), "Permit Update", emailBody);
                logDebug("Email sent to: " + contact.getEmail());
                return true;
            }
        } catch (err) {
            logDebug("Email failed: " + err.message);
            return false;
        }
    }
    """
    
    try:
        result = llm_helper.analyze_code_semantics(test_code, "Accela permit notification script")
        
        if result.get('confidence', 0) > 0.5:
            print("✅ Code semantic analysis working")
            print(f"   Purpose: {result.get('purpose', 'N/A')}")
            print(f"   Confidence: {result.get('confidence', 0):.2f}")
        else:
            print("⚠️ Code semantic analysis returned low confidence")
            print(f"   Result: {result}")
        
    except Exception as e:
        print(f"❌ Code semantic analysis failed: {e}")
        return False
    
    # Test implementation reasoning
    print("\n💡 Testing Implementation Reasoning...")
    
    test_implementations = [
        {
            'county': 'asheville',
            'score': 0.85,
            'metadata': {
                'functions': ['emailContact', 'validateAddress', 'addCondition'],
                'complexity': 'low',
                'doc_quality': 'excellent'
            }
        },
        {
            'county': 'santa_barbara',
            'score': 0.72,
            'metadata': {
                'functions': ['sendNotification', 'checkStatus'],
                'complexity': 'medium',
                'doc_quality': 'good'
            }
        }
    ]
    
    try:
        reasoning = llm_helper.generate_implementation_reasoning(
            test_implementations, 
            "email notification when permit is issued"
        )
        
        if reasoning and "failed" not in reasoning.lower():
            print("✅ Implementation reasoning working")
            print(f"   Reasoning: {reasoning[:100]}...")
        else:
            print("⚠️ Implementation reasoning returned error")
            print(f"   Result: {reasoning}")
        
    except Exception as e:
        print(f"❌ Implementation reasoning failed: {e}")
        return False
    
    print("\n🎉 LLM Integration Test Complete!")
    print("✅ All LLM features are working correctly")
    return True

def test_without_llm():
    """Test that system works without LLM"""
    
    print("\n🔧 Testing System Without LLM")
    print("=" * 50)
    
    # Test with no API key
    llm_helper = LLMHelper(None)
    
    if llm_helper.available:
        print("❌ LLMHelper should not be available without API key")
        return False
    
    print("✅ LLMHelper correctly unavailable without API key")
    
    # Test graceful degradation
    result = llm_helper.analyze_code_semantics("test code", "test context")
    
    if result.get('confidence', 1) == 0.0:
        print("✅ Code analysis gracefully degrades without LLM")
    else:
        print("❌ Code analysis should return 0 confidence without LLM")
        return False
    
    reasoning = llm_helper.generate_implementation_reasoning([], "test query")
    
    if "not available" in reasoning.lower():
        print("✅ Implementation reasoning gracefully degrades without LLM")
    else:
        print("❌ Implementation reasoning should indicate unavailability")
        return False
    
    print("✅ System works correctly without LLM")
    return True

def main():
    """Main test execution"""
    
    print("🚀 Accela Knowledge Base LLM Integration Test")
    print("=" * 60)
    
    # Test system without LLM first
    if not test_without_llm():
        print("\n❌ System failed without LLM - this is a critical issue")
        sys.exit(1)
    
    # Test with LLM if available
    llm_success = test_llm_availability()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    
    if llm_success:
        print("✅ LLM Integration: WORKING")
        print("✅ Rule-based Fallback: WORKING")
        print("🎯 Recommendation: Use with OPENAI_API_KEY for enhanced intelligence")
    else:
        print("⚠️ LLM Integration: NOT AVAILABLE")
        print("✅ Rule-based Fallback: WORKING")
        print("🎯 Recommendation: System fully functional with rule-based intelligence")
    
    print("\nNext Steps:")
    if llm_success:
        print("1. Run: python demo_agentic_system.py (with LLM enhancement)")
        print("2. Start API: python agentic_api_endpoints.py")
    else:
        print("1. Optional: Set OPENAI_API_KEY for enhanced intelligence")
        print("2. Run: python demo_agentic_system.py (rule-based intelligence)")
        print("3. Start API: python agentic_api_endpoints.py")

if __name__ == "__main__":
    main()
