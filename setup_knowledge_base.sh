#!/bin/bash

# Accela Knowledge Base Setup Script
# This script sets up the complete knowledge base system

set -e  # Exit on any error

echo "🚀 Setting up Accela Knowledge Base..."

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Check if src directory exists
if [ ! -d "src" ]; then
    echo "📁 Source directory not found. Creating and cloning repositories..."
    chmod +x install_repos.sh
    ./install_repos.sh
else
    echo "✅ Source directory found"
fi

# Step 1: Extract metadata from all scripts
echo "🔍 Step 1: Extracting metadata from Accela scripts..."
python knowledge_base_setup.py

# Check if metadata was created successfully
if [ ! -f "accela_metadata.json" ]; then
    echo "❌ Failed to create metadata file"
    exit 1
fi

echo "✅ Metadata extraction completed"

# Step 2: Build vector database
echo "🧠 Step 2: Building vector database..."
python vector_db_builder.py

# Check if vector index was created
if [ ! -d "vector_index" ]; then
    echo "❌ Failed to create vector index"
    exit 1
fi

echo "✅ Vector database created successfully"

# Step 3: Test the API
echo "🧪 Step 3: Testing the knowledge base API..."

# Start API in background for testing
echo "Starting API server for testing..."
python knowledge_base_api.py &
API_PID=$!

# Wait for API to start
sleep 5

# Test basic endpoints
echo "Testing API endpoints..."

# Test root endpoint
curl -s http://localhost:8000/ > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ API root endpoint working"
else
    echo "❌ API root endpoint failed"
fi

# Test search endpoint
curl -s -X POST "http://localhost:8000/search/semantic" \
     -H "Content-Type: application/json" \
     -d '{"query": "email notification", "k": 3}' > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Search endpoint working"
else
    echo "❌ Search endpoint failed"
fi

# Stop the test API server
kill $API_PID 2>/dev/null || true

echo ""
echo "🎉 Knowledge Base Setup Complete!"
echo ""
echo "📊 Summary:"
echo "  - Metadata extracted from all Accela scripts"
echo "  - Vector database created with embeddings"
echo "  - API server tested and working"
echo ""
echo "🚀 Next Steps:"
echo ""
echo "1. Start the API server:"
echo "   source venv/bin/activate"
echo "   python knowledge_base_api.py"
echo ""
echo "2. Access the API at: http://localhost:8000"
echo "   - API docs: http://localhost:8000/docs"
echo "   - Interactive docs: http://localhost:8000/redoc"
echo ""
echo "3. Example API calls:"
echo "   # Semantic search"
echo "   curl -X POST 'http://localhost:8000/search/semantic' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"query\": \"permit notification email\", \"k\": 5}'"
echo ""
echo "   # Function search"
echo "   curl 'http://localhost:8000/search/code?function=emailContact&county=asheville'"
echo ""
echo "   # Compare counties"
echo "   curl -X POST 'http://localhost:8000/compare/counties' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"counties\": [\"asheville\", \"santa_barbara\"], \"script_type\": \"event\"}'"
echo ""
echo "   # Get statistics"
echo "   curl 'http://localhost:8000/stats'"
echo ""
echo "4. For LLM integration, use the semantic search endpoint with natural language queries"
echo ""
echo "📁 Files created:"
echo "  - accela_metadata.json (script metadata)"
echo "  - vector_index/ (vector database)"
echo "  - venv/ (Python virtual environment)"
echo ""
echo "Happy querying! 🎯"
