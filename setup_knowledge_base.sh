#!/bin/bash

# Accela Agentic Knowledge Base Setup Script
# This script sets up the complete agentic knowledge base system

set -e  # Exit on any error

echo "🤖 Setting up Accela Agentic Knowledge Base..."

# Check if Python 3.8+ is available
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python 3.8+ is required. Current version: $python_version"
    exit 1
fi

echo "✅ Python version check passed: $python_version"

# Create virtual environment
echo "📦 Creating virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📚 Installing dependencies..."
pip install -r requirements.txt

# Check if OpenAI API key is set for enhanced LLM intelligence
if [ -z "$OPENAI_API_KEY" ]; then
    echo "ℹ️  Optional: Set OPENAI_API_KEY environment variable for enhanced LLM intelligence"
    echo "   export OPENAI_API_KEY='your-api-key-here'"
    echo "   System will work perfectly without it using rule-based intelligence"
else
    echo "✅ OpenAI API key detected - LLM intelligence will be enabled"
fi

# Check if src directory exists
if [ ! -d "src" ]; then
    echo "📁 Source directory not found. Creating and cloning repositories..."
    chmod +x install_repos.sh
    ./install_repos.sh
else
    echo "✅ Source directory found"
fi

# Step 1: Extract metadata and build knowledge graph
echo "🔍 Step 1: Extracting metadata and building knowledge graph..."
python knowledge_base_setup.py

# Check if metadata was created successfully
if [ ! -f "accela_metadata.json" ]; then
    echo "❌ Failed to create metadata file"
    exit 1
fi

echo "✅ Metadata extraction completed"

# Step 2: Test LLM integration
echo "🧪 Step 2: Testing LLM integration..."
python test_llm_integration.py

# Step 3: Test the agentic system
echo "🧪 Step 3: Testing the agentic system..."

# Test multi-agent orchestration
echo "Testing multi-agent orchestration..."
python -c "
import asyncio
from multi_agent_orchestrator import demo_multi_agent_orchestration
try:
    asyncio.run(demo_multi_agent_orchestration())
    print('✅ Multi-agent orchestration test passed')
except Exception as e:
    print(f'❌ Multi-agent test failed: {e}')
    exit(1)
"

# Step 4: Start the agentic API
echo "🚀 Step 4: Testing the agentic API..."

# Start API in background for testing
echo "Starting agentic API server for testing..."
python agentic_api_endpoints.py &
API_PID=$!

# Wait for API to start
sleep 10

# Test basic endpoints
echo "Testing agentic API endpoints..."

# Test root endpoint
curl -s http://localhost:8001/ > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Agentic API root endpoint working"
else
    echo "❌ Agentic API root endpoint failed"
fi

# Test agentic query endpoint
curl -s -X POST "http://localhost:8001/agentic/query" \
     -H "Content-Type: application/json" \
     -d '{"query": "email notification when permit is issued", "use_case": "permit_notification"}' > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Agentic query endpoint working"
else
    echo "❌ Agentic query endpoint failed"
fi

# Stop the test API server
kill $API_PID 2>/dev/null || true

echo ""
echo "🎉 Agentic Knowledge Base Setup Complete!"
echo ""
echo "📊 Summary:"
echo "  - Metadata extracted from all Accela scripts"
echo "  - Knowledge graph built with relationships"
echo "  - Multi-agent system initialized"
echo "  - Agentic API server tested and working"
echo ""
echo "🚀 Next Steps:"
echo ""
echo "1. Start the agentic API server:"
echo "   source venv/bin/activate"
echo "   python agentic_api_endpoints.py"
echo ""
echo "2. Access the agentic API at: http://localhost:8001"
echo "   - API docs: http://localhost:8001/docs"
echo "   - Interactive docs: http://localhost:8001/redoc"
echo ""
echo "3. Example agentic API calls:"
echo "   # Intelligent query with multi-agent analysis"
echo "   curl -X POST 'http://localhost:8001/agentic/query' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"query\": \"email notification when permit is issued\", \"use_case\": \"permit_notification\", \"target_counties\": [\"asheville\", \"santa_barbara\"]}'"
echo ""
echo "   # Advanced county comparison"
echo "   curl -X POST 'http://localhost:8001/agentic/compare' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"use_case\": \"permit_workflow\", \"counties\": [\"asheville\", \"santa_barbara\", \"dayton\"]}'"
echo ""
echo "   # Best practice identification"
echo "   curl -X POST 'http://localhost:8001/agentic/best-practices' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"domain\": \"permits\", \"min_counties\": 2, \"quality_threshold\": 0.7}'"
echo ""
echo "   # Generate implementation plan"
echo "   curl -X POST 'http://localhost:8001/agentic/implementation-plan' \\"
echo "        -H 'Content-Type: application/json' \\"
echo "        -d '{\"selected_implementation\": {\"county\": \"asheville\", \"metadata\": {\"functions\": [\"emailContact\", \"addCondition\"]}}, \"target_environment\": \"production\", \"timeline_weeks\": 4}'"
echo ""
echo "   # Knowledge graph statistics"
echo "   curl 'http://localhost:8001/graph/stats'"
echo ""
echo "4. Key Features:"
echo "   🤖 Multi-agent intelligence for optimal recommendations"
echo "   🕸️ Graph-based knowledge understanding"
echo "   📊 Cross-county analysis and best practices"
echo "   🎯 Intelligent synthesis of solutions"
echo "   📋 Automated implementation planning"
echo ""
echo "📁 Files created:"
echo "  - accela_metadata.json (script metadata)"
echo "  - accela_knowledge_graph.gexf (graph export)"
echo "  - venv/ (Python virtual environment)"
echo ""
echo "🤖 Ready for intelligent Accela development! 🎯"
