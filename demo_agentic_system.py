#!/usr/bin/env python3
"""
Demo script for Accela Agentic Knowledge Base
Showcases the intelligent multi-agent system capabilities
"""

import asyncio
import json
import uuid
from datetime import datetime

from agentic_graph_system import AccelaKnowledgeGraph
from multi_agent_orchestrator import MultiAgentOrchestrator, OrchestrationRequest

async def demo_agentic_capabilities():
    """Comprehensive demo of agentic system capabilities"""
    
    print("🤖 Accela Agentic Knowledge Base Demo")
    print("=" * 60)
    
    # Initialize the system
    print("\n🔧 Initializing agentic system...")
    
    try:
        # Build knowledge graph
        knowledge_graph = AccelaKnowledgeGraph()
        knowledge_graph.build_graph_from_metadata()
        
        # Initialize orchestrator
        orchestrator = MultiAgentOrchestrator(knowledge_graph)
        
        print("✅ System initialized successfully")
        
        # Get graph statistics
        stats = knowledge_graph.get_graph_stats()
        print(f"📊 Knowledge Graph: {stats['nodes']} nodes, {stats['edges']} edges")
        print(f"   Counties: {stats['counties']}, Functions: {stats['functions']}")
        
    except FileNotFoundError:
        print("❌ Metadata file not found. Please run:")
        print("   python knowledge_base_setup.py")
        return
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return
    
    # Demo scenarios
    demo_scenarios = [
        {
            "title": "🔔 Email Notification Implementation",
            "query": "email notification when permit is issued",
            "use_case": "permit_notification",
            "counties": ["asheville", "santa_barbara", "dayton"],
            "constraints": {"complexity": "low", "documentation": "good"}
        },
        {
            "title": "🏗️ Inspection Scheduling Workflow",
            "query": "automatically schedule inspections when permit is ready",
            "use_case": "inspection_automation",
            "counties": ["asheville", "leon", "lancaster"],
            "constraints": {"complexity": "medium"}
        },
        {
            "title": "📍 Address Validation System",
            "query": "validate and standardize property addresses",
            "use_case": "address_validation",
            "counties": None,  # All counties
            "constraints": {"usage_frequency": "high"}
        }
    ]
    
    # Run demo scenarios
    for i, scenario in enumerate(demo_scenarios, 1):
        print(f"\n{scenario['title']}")
        print("-" * 50)
        
        # Create orchestration request
        request = OrchestrationRequest(
            request_id=str(uuid.uuid4()),
            query=scenario['query'],
            use_case=scenario['use_case'],
            target_counties=scenario['counties'],
            constraints=scenario['constraints']
        )
        
        print(f"🎯 Query: {scenario['query']}")
        if scenario['counties']:
            print(f"🏛️ Target Counties: {', '.join(scenario['counties'])}")
        else:
            print("🏛️ Target Counties: All available")
        
        try:
            # Execute orchestration
            start_time = datetime.now()
            result = await orchestrator.orchestrate(request)
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Display results
            print(f"\n📊 Results (processed in {processing_time:.2f}s):")
            print(f"   Confidence: {result.confidence:.2f}")
            
            if result.best_implementation:
                best = result.best_implementation
                print(f"\n🏆 Best Implementation:")
                print(f"   County: {best.get('county', 'Unknown')}")
                print(f"   Score: {best.get('score', 0):.3f}")
                print(f"   Complexity: {best.get('metadata', {}).get('complexity', 'Unknown')}")
                print(f"   Documentation: {best.get('metadata', {}).get('doc_quality', 'Unknown')}")
                
                functions = best.get('metadata', {}).get('functions', [])
                if functions:
                    print(f"   Key Functions: {', '.join(functions[:3])}")
            
            if result.alternatives:
                print(f"\n🔄 Alternatives:")
                for j, alt in enumerate(result.alternatives[:2], 1):
                    print(f"   {j}. {alt.get('county', 'Unknown')} (score: {alt.get('score', 0):.3f})")
            
            # Show synthesis insights
            synthesis = result.synthesis
            if synthesis.get('optimal_approach'):
                optimal = synthesis['optimal_approach']
                print(f"\n🧠 Agent Synthesis:")
                print(f"   Primary Approach: {optimal.get('primary_county', 'Unknown')}")
                print(f"   Recommended Complexity: {optimal.get('complexity', 'Unknown')}")
                
                if synthesis.get('implementation_plan'):
                    plan = synthesis['implementation_plan']
                    print(f"   Implementation Phases: {len(plan)}")
                    if plan:
                        print(f"   Estimated Duration: {plan[-1].get('phase', 0)} phases")
            
            # Show top recommendations
            if result.recommendations:
                print(f"\n💡 Top Recommendations:")
                for j, rec in enumerate(result.recommendations[:3], 1):
                    print(f"   {j}. {rec}")
            
        except Exception as e:
            print(f"❌ Orchestration failed: {e}")
        
        if i < len(demo_scenarios):
            print("\n" + "="*60)
    
    # Demo enhanced graph analysis capabilities with Accela naming convention
    print(f"\n🕸️ Enhanced Knowledge Graph Analysis")
    print("-" * 50)

    try:
        # County expertise analysis with Accela naming
        print("📈 Enhanced County Expertise Analysis:")
        domains = ["permits", "licenses", "building", "business", "planning"]

        for domain in domains:
            expertise = knowledge_graph.get_county_expertise(domain)
            if expertise:
                best_county = max(expertise.items(), key=lambda x: x[1])
                print(f"   {domain.title()}: {best_county[0]} (score: {best_county[1]:.3f})")

        # Event prefix analysis
        print(f"\n🎯 Accela Event Prefix Analysis:")
        event_analysis = knowledge_graph.get_event_prefix_analysis()

        print("   Most Common Event Prefixes:")
        for prefix, count in event_analysis['most_common_prefixes'][:5]:
            description = event_analysis['prefix_usage'][prefix]['description']
            counties = event_analysis['prefix_usage'][prefix]['counties']
            print(f"     {prefix}: {count} scripts ({description})")
            print(f"       Used by: {', '.join(counties)}")

        print("   County Event Prefix Diversity:")
        for county, data in list(event_analysis['county_prefix_diversity'].items())[:5]:
            print(f"     {county}: {data['prefix_count']} different prefixes")
            if data['prefix_count'] > 0:
                print(f"       Prefixes: {', '.join(data['prefixes'][:3])}{'...' if len(data['prefixes']) > 3 else ''}")

        # Module-Application Type Matrix
        print(f"\n📊 Module vs Application Type Matrix:")
        matrix_analysis = knowledge_graph.get_module_application_matrix()

        print("   Coverage Statistics:")
        stats = matrix_analysis['coverage_stats']
        print(f"     Total Modules: {stats['total_modules']}")
        print(f"     Total Application Types: {stats['total_app_types']}")
        print(f"     Avg Modules per County: {stats['avg_modules_per_county']:.1f}")

        print("   Top Module-Application Combinations:")
        matrix = matrix_analysis['module_app_matrix']
        combinations = []
        for module, app_types in matrix.items():
            for app_type, counties in app_types.items():
                combinations.append((f"{module}→{app_type}", len(counties), counties))

        combinations.sort(key=lambda x: x[1], reverse=True)
        for combo, county_count, counties in combinations[:5]:
            print(f"     {combo}: {county_count} counties ({', '.join(counties)})")

        # Common patterns with enhanced context
        print(f"\n🔍 Enhanced Implementation Patterns:")
        patterns = knowledge_graph.find_implementation_patterns("function_combinations")

        if patterns:
            for pattern, counties in list(patterns.items())[:3]:
                unique_counties = len(set(counties))
                print(f"   Pattern: {pattern}")
                print(f"   Used by {unique_counties} counties: {', '.join(set(counties))}")
        else:
            print("   No common patterns found")

        # Export enhanced graph for visualization
        print(f"\n📊 Exporting enhanced knowledge graph...")
        knowledge_graph.export_graph("demo_enhanced_accela_graph.gexf")
        print("   Enhanced graph exported to: demo_enhanced_accela_graph.gexf")
        print("   (Includes Accela naming convention nodes and relationships)")
        print("   (Can be opened in Gephi or other graph visualization tools)")

    except Exception as e:
        print(f"❌ Enhanced graph analysis failed: {e}")
    
    # Summary
    print(f"\n🎉 Demo Complete!")
    print("=" * 60)
    print("Key Capabilities Demonstrated:")
    print("🤖 Multi-agent intelligent analysis")
    print("🕸️ Enhanced graph-based knowledge with Accela naming convention")
    print("🎯 Optimal implementation recommendations using event prefixes")
    print("📊 Cross-county comparison with module-application matrix")
    print("💡 Automated best practice identification with naming context")
    print("📋 Implementation planning and risk assessment")
    print("🏷️ Accela naming convention parsing and analysis")
    print("🎪 Event prefix expertise and diversity analysis")
    print("")
    print("Next Steps:")
    print("1. Start the agentic API: python agentic_api_endpoints.py")
    print("2. Access API docs: http://localhost:8001/docs")
    print("3. Integrate with your LLM workflows")
    print("4. Visualize the enhanced knowledge graph with Gephi")
    print("5. Explore Accela naming convention insights")

def main():
    """Main execution function"""
    try:
        asyncio.run(demo_agentic_capabilities())
    except KeyboardInterrupt:
        print("\n\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("Make sure you've run: python knowledge_base_setup.py")

if __name__ == "__main__":
    main()
