"""
Recommender agent for generating implementation recommendations
"""

from typing import Dict, Any, List
from ..core.models import Agent<PERSON><PERSON>, AgentResponse, Agent<PERSON><PERSON>
from .base_agent import BaseAgent


class RecommenderAgent(BaseAgent):
    """Agent responsible for generating implementation recommendations"""
    
    def __init__(self, knowledge_graph, llm_helper=None):
        super().__init__("RecommenderAgent", AgentRole.RECOMMENDER, llm_helper)
        self.knowledge_graph = knowledge_graph
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """
        Execute recommendation task
        
        Args:
            task: Recommendation task to execute
            
        Returns:
            AgentResponse with recommendations
        """
        
        self.logger.info(f"Executing recommendation task: {task.query}")
        
        try:
            # Get analyzed implementations and comparisons from context
            implementations = task.context.get('implementations', [])
            comparisons = task.context.get('comparisons', [])
            
            # Generate recommendations
            recommendations = self._generate_recommendations(implementations, comparisons, task.query)
            
            # Calculate confidence
            confidence = self._calculate_confidence(recommendations, implementations)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(recommendations, implementations)
            
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={
                    'recommendations': recommendations,
                    'best_implementation': recommendations[0] if recommendations else None,
                    'alternatives': recommendations[1:4] if len(recommendations) > 1 else []
                },
                confidence=confidence,
                reasoning=reasoning,
                recommendations=[r['summary'] for r in recommendations[:3]]
            )
            
        except Exception as e:
            self.logger.error(f"Recommendation task failed: {e}")
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={'error': str(e)},
                confidence=0.0,
                reasoning=f"Recommendation failed: {str(e)}",
                recommendations=[]
            )
    
    def _generate_recommendations(self, implementations: List[Dict], comparisons: List[Dict], query: str) -> List[Dict[str, Any]]:
        """Generate ranked recommendations"""
        
        if not implementations:
            return []
        
        recommendations = []
        
        for impl in implementations:
            recommendation = self._create_recommendation(impl, comparisons, query)
            recommendations.append(recommendation)
        
        # Sort by recommendation score
        recommendations.sort(key=lambda x: x['recommendation_score'], reverse=True)
        
        return recommendations
    
    def _create_recommendation(self, implementation: Dict, comparisons: List[Dict], query: str) -> Dict[str, Any]:
        """Create recommendation for a single implementation"""
        
        county = implementation.get('county', 'unknown')
        metadata = implementation.get('metadata', {})
        
        # Base score from relevance
        base_score = implementation.get('relevance_score', 0)
        
        # Quality factors
        quality_score = self._calculate_quality_score(metadata)
        
        # Similarity factors (how well it aligns with other implementations)
        similarity_score = self._calculate_similarity_score(county, comparisons)
        
        # Combine scores
        recommendation_score = (base_score * 0.5) + (quality_score * 0.3) + (similarity_score * 0.2)
        
        recommendation = {
            'county': county,
            'recommendation_score': recommendation_score,
            'base_relevance': base_score,
            'quality_score': quality_score,
            'similarity_score': similarity_score,
            'metadata': metadata,
            'strengths': self._identify_strengths(metadata),
            'considerations': self._identify_considerations(metadata),
            'summary': self._create_summary(county, metadata, recommendation_score)
        }
        
        # Add LLM reasoning if available
        if self.has_llm_capability():
            try:
                llm_reasoning = self.llm_helper.generate_implementation_reasoning([implementation], query)
                recommendation['llm_reasoning'] = llm_reasoning
            except Exception as e:
                self.logger.warning(f"LLM reasoning failed: {e}")
        
        return recommendation
    
    def _calculate_quality_score(self, metadata: Dict) -> float:
        """Calculate quality score based on metadata"""
        
        score = 0.0
        
        # Documentation quality
        doc_quality = metadata.get('doc_quality', 'poor')
        if doc_quality == 'excellent':
            score += 0.4
        elif doc_quality == 'good':
            score += 0.2
        
        # Complexity (lower is better for recommendations)
        complexity = metadata.get('complexity', 'medium')
        if complexity == 'low':
            score += 0.3
        elif complexity == 'medium':
            score += 0.1
        
        # Function count (more functions = more comprehensive)
        function_count = len(metadata.get('functions', []))
        if function_count > 5:
            score += 0.2
        elif function_count > 2:
            score += 0.1
        
        # Event prefix (having a clear event pattern is good)
        if metadata.get('event_prefix'):
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_similarity_score(self, county: str, comparisons: List[Dict]) -> float:
        """Calculate how well this implementation aligns with others"""
        
        if not comparisons:
            return 0.5  # Neutral score if no comparisons
        
        # Find comparisons involving this county
        relevant_comparisons = []
        for comp in comparisons:
            if (comp['implementation_1']['county'] == county or 
                comp['implementation_2']['county'] == county):
                relevant_comparisons.append(comp)
        
        if not relevant_comparisons:
            return 0.5
        
        # Average similarity with other implementations
        similarities = [comp['overall_similarity'] for comp in relevant_comparisons]
        avg_similarity = sum(similarities) / len(similarities)
        
        # Moderate similarity is good (not too unique, not too common)
        if 0.3 <= avg_similarity <= 0.7:
            return 0.8
        elif avg_similarity > 0.7:
            return 0.6  # Very similar might indicate redundancy
        else:
            return 0.4  # Very different might be risky
    
    def _identify_strengths(self, metadata: Dict) -> List[str]:
        """Identify strengths of the implementation"""
        
        strengths = []
        
        if metadata.get('doc_quality') == 'excellent':
            strengths.append("Excellent documentation")
        elif metadata.get('doc_quality') == 'good':
            strengths.append("Good documentation")
        
        if metadata.get('complexity') == 'low':
            strengths.append("Low complexity - easy to understand and maintain")
        
        function_count = len(metadata.get('functions', []))
        if function_count > 5:
            strengths.append(f"Comprehensive implementation ({function_count} functions)")
        
        if metadata.get('event_prefix'):
            strengths.append(f"Clear event pattern ({metadata['event_prefix']})")
        
        return strengths
    
    def _identify_considerations(self, metadata: Dict) -> List[str]:
        """Identify considerations or potential issues"""
        
        considerations = []
        
        if metadata.get('complexity') == 'high':
            considerations.append("High complexity - may require more development time")
        
        if metadata.get('doc_quality') == 'poor':
            considerations.append("Limited documentation - may need additional analysis")
        
        function_count = len(metadata.get('functions', []))
        if function_count < 2:
            considerations.append("Limited functionality - may need extension")
        
        if not metadata.get('event_prefix'):
            considerations.append("No clear event pattern - verify integration approach")
        
        return considerations
    
    def _create_summary(self, county: str, metadata: Dict, score: float) -> str:
        """Create a summary for the recommendation"""
        
        summary_parts = [f"{county} implementation"]
        
        # Add key characteristics
        if metadata.get('complexity') == 'low':
            summary_parts.append("simple approach")
        elif metadata.get('complexity') == 'high':
            summary_parts.append("comprehensive solution")
        
        if metadata.get('doc_quality') == 'excellent':
            summary_parts.append("well documented")
        
        function_count = len(metadata.get('functions', []))
        if function_count > 5:
            summary_parts.append(f"{function_count} functions")
        
        # Add score indication
        if score > 0.8:
            summary_parts.append("highly recommended")
        elif score > 0.6:
            summary_parts.append("good option")
        elif score > 0.4:
            summary_parts.append("viable alternative")
        
        return " - ".join(summary_parts)
    
    def _calculate_confidence(self, recommendations: List[Dict], implementations: List[Dict]) -> float:
        """Calculate confidence in recommendations"""
        
        if not recommendations:
            return 0.0
        
        # Base confidence on number of implementations
        base_confidence = min(len(implementations) / 5.0, 1.0)
        
        # Adjust based on score distribution
        scores = [r['recommendation_score'] for r in recommendations]
        if scores:
            max_score = max(scores)
            score_spread = max_score - min(scores) if len(scores) > 1 else max_score
            
            # Higher confidence when there's a clear winner
            spread_factor = min(score_spread * 2, 1.0)
            
            return base_confidence * (0.5 + spread_factor * 0.5)
        
        return base_confidence
    
    def _generate_reasoning(self, recommendations: List[Dict], implementations: List[Dict]) -> str:
        """Generate reasoning for recommendations"""
        
        if not recommendations:
            return "No recommendations generated"
        
        best = recommendations[0]
        
        reasoning_parts = [
            f"Top recommendation: {best['county']} (score: {best['recommendation_score']:.2f})",
            f"Based on: relevance ({best['base_relevance']:.2f}), quality ({best['quality_score']:.2f})"
        ]
        
        if len(recommendations) > 1:
            reasoning_parts.append(f"Alternative: {recommendations[1]['county']}")
        
        return " | ".join(reasoning_parts)
