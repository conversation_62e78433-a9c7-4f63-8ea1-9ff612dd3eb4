"""
Comparator agent for comparing implementations across counties
"""

from typing import Dict, Any, List
from ..core.models import Agent<PERSON><PERSON>, AgentResponse, AgentR<PERSON>
from .base_agent import BaseAgent


class ComparatorAgent(BaseAgent):
    """Agent responsible for comparing implementations across counties"""
    
    def __init__(self, knowledge_graph, llm_helper=None):
        super().__init__("ComparatorAgent", AgentRole.COMPARATOR, llm_helper)
        self.knowledge_graph = knowledge_graph
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """
        Execute comparison task
        
        Args:
            task: Comparison task to execute
            
        Returns:
            AgentResponse with comparison results
        """
        
        self.logger.info(f"Executing comparison task: {task.query}")
        
        try:
            # Get implementations to compare
            implementations = task.context.get('implementations', [])
            
            if len(implementations) < 2:
                return AgentResponse(
                    task_id=task.task_id,
                    agent_role=self.role,
                    result={'error': 'Need at least 2 implementations to compare'},
                    confidence=0.0,
                    reasoning="Insufficient implementations for comparison",
                    recommendations=[]
                )
            
            # Perform comparisons
            comparisons = self._compare_implementations(implementations)
            
            # Calculate confidence
            confidence = self._calculate_confidence(comparisons)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(comparisons)
            
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={
                    'comparisons': comparisons,
                    'summary': self._create_comparison_summary(comparisons)
                },
                confidence=confidence,
                reasoning=reasoning,
                recommendations=self._generate_recommendations(comparisons)
            )
            
        except Exception as e:
            self.logger.error(f"Comparison task failed: {e}")
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={'error': str(e)},
                confidence=0.0,
                reasoning=f"Comparison failed: {str(e)}",
                recommendations=[]
            )
    
    def _compare_implementations(self, implementations: List[Dict]) -> List[Dict[str, Any]]:
        """Compare implementations pairwise"""
        
        comparisons = []
        
        for i in range(len(implementations)):
            for j in range(i + 1, len(implementations)):
                impl1 = implementations[i]
                impl2 = implementations[j]
                
                comparison = self._compare_pair(impl1, impl2)
                comparisons.append(comparison)
        
        return comparisons
    
    def _compare_pair(self, impl1: Dict, impl2: Dict) -> Dict[str, Any]:
        """Compare two implementations"""
        
        comparison = {
            'implementation_1': {
                'county': impl1.get('county', 'unknown'),
                'score': impl1.get('relevance_score', 0)
            },
            'implementation_2': {
                'county': impl2.get('county', 'unknown'),
                'score': impl2.get('relevance_score', 0)
            }
        }
        
        # Compare functions
        functions1 = set(impl1.get('metadata', {}).get('functions', []))
        functions2 = set(impl2.get('metadata', {}).get('functions', []))
        
        function_similarity = self._calculate_function_similarity(functions1, functions2)
        comparison['function_similarity'] = function_similarity
        
        # Compare complexity
        complexity1 = impl1.get('metadata', {}).get('complexity', 'medium')
        complexity2 = impl2.get('metadata', {}).get('complexity', 'medium')
        comparison['complexity_comparison'] = {
            'impl1': complexity1,
            'impl2': complexity2,
            'same': complexity1 == complexity2
        }
        
        # Compare documentation quality
        doc1 = impl1.get('metadata', {}).get('doc_quality', 'poor')
        doc2 = impl2.get('metadata', {}).get('doc_quality', 'poor')
        comparison['documentation_comparison'] = {
            'impl1': doc1,
            'impl2': doc2,
            'same': doc1 == doc2
        }
        
        # Compare event prefixes
        prefix1 = impl1.get('metadata', {}).get('event_prefix')
        prefix2 = impl2.get('metadata', {}).get('event_prefix')
        comparison['event_prefix_comparison'] = {
            'impl1': prefix1,
            'impl2': prefix2,
            'same': prefix1 == prefix2
        }
        
        # Overall similarity score
        comparison['overall_similarity'] = self._calculate_overall_similarity(comparison)
        
        return comparison
    
    def _calculate_function_similarity(self, functions1: set, functions2: set) -> float:
        """Calculate Jaccard similarity between function sets"""
        
        if not functions1 and not functions2:
            return 1.0
        
        if not functions1 or not functions2:
            return 0.0
        
        intersection = len(functions1.intersection(functions2))
        union = len(functions1.union(functions2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_overall_similarity(self, comparison: Dict) -> float:
        """Calculate overall similarity score"""
        
        scores = []
        weights = []
        
        # Function similarity (highest weight)
        scores.append(comparison['function_similarity'])
        weights.append(0.4)
        
        # Event prefix similarity
        prefix_sim = 1.0 if comparison['event_prefix_comparison']['same'] else 0.0
        scores.append(prefix_sim)
        weights.append(0.3)
        
        # Complexity similarity
        complexity_sim = 1.0 if comparison['complexity_comparison']['same'] else 0.5
        scores.append(complexity_sim)
        weights.append(0.2)
        
        # Documentation similarity
        doc_sim = 1.0 if comparison['documentation_comparison']['same'] else 0.5
        scores.append(doc_sim)
        weights.append(0.1)
        
        # Calculate weighted average
        weighted_sum = sum(s * w for s, w in zip(scores, weights))
        total_weight = sum(weights)
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _calculate_confidence(self, comparisons: List[Dict]) -> float:
        """Calculate confidence in comparison results"""
        
        if not comparisons:
            return 0.0
        
        # Base confidence on number of comparisons
        base_confidence = min(len(comparisons) / 5.0, 1.0)
        
        # Adjust based on similarity scores
        avg_similarity = sum(c['overall_similarity'] for c in comparisons) / len(comparisons)
        
        # Higher confidence when similarities are clear (either high or low)
        clarity_factor = 1.0 - abs(avg_similarity - 0.5) * 2
        
        return base_confidence * (0.5 + clarity_factor * 0.5)
    
    def _generate_reasoning(self, comparisons: List[Dict]) -> str:
        """Generate reasoning for comparison results"""
        
        if not comparisons:
            return "No comparisons performed"
        
        # Find most and least similar pairs
        most_similar = max(comparisons, key=lambda x: x['overall_similarity'])
        least_similar = min(comparisons, key=lambda x: x['overall_similarity'])
        
        reasoning_parts = [
            f"Compared {len(comparisons)} implementation pairs",
            f"Most similar: {most_similar['implementation_1']['county']} vs {most_similar['implementation_2']['county']} ({most_similar['overall_similarity']:.2f})",
            f"Least similar: {least_similar['implementation_1']['county']} vs {least_similar['implementation_2']['county']} ({least_similar['overall_similarity']:.2f})"
        ]
        
        return " | ".join(reasoning_parts)
    
    def _create_comparison_summary(self, comparisons: List[Dict]) -> Dict[str, Any]:
        """Create summary of comparison results"""
        
        if not comparisons:
            return {}
        
        # Calculate statistics
        similarities = [c['overall_similarity'] for c in comparisons]
        function_similarities = [c['function_similarity'] for c in comparisons]
        
        # Count same event prefixes
        same_prefixes = sum(1 for c in comparisons if c['event_prefix_comparison']['same'])
        
        # Count same complexity
        same_complexity = sum(1 for c in comparisons if c['complexity_comparison']['same'])
        
        return {
            'total_comparisons': len(comparisons),
            'average_similarity': sum(similarities) / len(similarities),
            'max_similarity': max(similarities),
            'min_similarity': min(similarities),
            'average_function_similarity': sum(function_similarities) / len(function_similarities),
            'same_event_prefix_count': same_prefixes,
            'same_complexity_count': same_complexity
        }
    
    def _generate_recommendations(self, comparisons: List[Dict]) -> List[str]:
        """Generate recommendations based on comparisons"""
        
        recommendations = []
        
        if not comparisons:
            return recommendations
        
        # Find most similar implementations
        most_similar = max(comparisons, key=lambda x: x['overall_similarity'])
        
        if most_similar['overall_similarity'] > 0.7:
            recommendations.append(
                f"High similarity between {most_similar['implementation_1']['county']} and "
                f"{most_similar['implementation_2']['county']} - consider code reuse"
            )
        
        # Check for function overlap
        high_function_overlap = [c for c in comparisons if c['function_similarity'] > 0.5]
        if high_function_overlap:
            recommendations.append(f"Found {len(high_function_overlap)} pairs with significant function overlap")
        
        # Check for consistent patterns
        same_prefix_count = sum(1 for c in comparisons if c['event_prefix_comparison']['same'])
        if same_prefix_count > len(comparisons) * 0.7:
            recommendations.append("Consistent event prefix usage across implementations")
        
        return recommendations
