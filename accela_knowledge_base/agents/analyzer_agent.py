"""
Analyzer agent for code analysis and semantic understanding
"""

from typing import Dict, Any, List
from ..core.models import Agent<PERSON><PERSON>, AgentResponse, AgentR<PERSON>
from .base_agent import BaseAgent


class AnalyzerAgent(BaseAgent):
    """Agent responsible for analyzing code and extracting insights"""
    
    def __init__(self, knowledge_graph, llm_helper=None):
        super().__init__("AnalyzerAgent", AgentRole.ANALYZER, llm_helper)
        self.knowledge_graph = knowledge_graph
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """
        Execute analysis task
        
        Args:
            task: Analysis task to execute
            
        Returns:
            AgentResponse with analysis results
        """
        
        self.logger.info(f"Executing analysis task: {task.query}")
        
        try:
            # Find relevant scripts based on query
            relevant_scripts = self._find_relevant_scripts(task.query)
            
            # Analyze scripts
            analysis_results = []
            for script in relevant_scripts:
                analysis = self._analyze_script(script, task.query)
                if analysis:
                    analysis_results.append(analysis)
            
            # Calculate confidence based on results
            confidence = self._calculate_confidence(analysis_results, task.query)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(analysis_results, task.query)
            
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={
                    'relevant_scripts': analysis_results,
                    'total_found': len(analysis_results),
                    'analysis_summary': self._create_analysis_summary(analysis_results)
                },
                confidence=confidence,
                reasoning=reasoning,
                recommendations=self._generate_recommendations(analysis_results)
            )
            
        except Exception as e:
            self.logger.error(f"Analysis task failed: {e}")
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={'error': str(e)},
                confidence=0.0,
                reasoning=f"Analysis failed: {str(e)}",
                recommendations=[]
            )
    
    def _find_relevant_scripts(self, query: str) -> List[Dict[str, Any]]:
        """Find scripts relevant to the query"""
        
        relevant_scripts = []
        query_lower = query.lower()
        
        # Search through graph nodes
        for node_id in self.knowledge_graph.graph.nodes():
            if node_id.startswith("script:"):
                node_data = self.knowledge_graph.graph.nodes[node_id]
                
                # Check if script is relevant
                relevance_score = self._calculate_script_relevance(node_data, query_lower)
                
                if relevance_score > 0:
                    script_info = {
                        'node_id': node_id,
                        'data': node_data,
                        'relevance_score': relevance_score
                    }
                    relevant_scripts.append(script_info)
        
        # Sort by relevance
        relevant_scripts.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return relevant_scripts[:20]  # Return top 20
    
    def _calculate_script_relevance(self, node_data: Dict[str, Any], query: str) -> float:
        """Calculate how relevant a script is to the query"""
        
        score = 0.0
        
        # Check functions
        functions = node_data.get('functions', [])
        for func in functions:
            if any(term in func.lower() for term in query.split()):
                score += 0.3
        
        # Check event prefix
        event_prefix = node_data.get('event_prefix', '')
        if event_prefix and any(term in event_prefix.lower() for term in query.split()):
            score += 0.2
        
        # Check module
        module = node_data.get('module', '')
        if module and any(term in module.lower() for term in query.split()):
            score += 0.2
        
        # Check application type
        app_type = node_data.get('application_type', '')
        if app_type and any(term in app_type.lower() for term in query.split()):
            score += 0.2
        
        return score
    
    def _analyze_script(self, script_info: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Analyze a single script"""
        
        node_data = script_info['data']
        
        analysis = {
            'script_id': script_info['node_id'],
            'county': node_data.get('county', 'unknown'),
            'relevance_score': script_info['relevance_score'],
            'metadata': {
                'functions': node_data.get('functions', []),
                'event_prefix': node_data.get('event_prefix'),
                'module': node_data.get('module'),
                'application_type': node_data.get('application_type'),
                'complexity': node_data.get('complexity', 'medium'),
                'doc_quality': node_data.get('doc_quality', 'poor')
            }
        }
        
        # Add LLM semantic analysis if available
        if self.has_llm_capability():
            try:
                # Get script content if available
                content = node_data.get('content', '')
                if content and len(content) > 100:
                    semantic_analysis = self.llm_helper.analyze_code_semantics(content, query)
                    analysis['semantic_analysis'] = semantic_analysis
            except Exception as e:
                self.logger.warning(f"LLM analysis failed for script: {e}")
        
        return analysis
    
    def _calculate_confidence(self, results: List[Dict], query: str) -> float:
        """Calculate confidence in analysis results"""
        
        if not results:
            return 0.0
        
        # Base confidence on number and quality of results
        base_confidence = min(len(results) / 10.0, 1.0)  # Up to 10 results = full confidence
        
        # Adjust based on relevance scores
        avg_relevance = sum(r.get('relevance_score', 0) for r in results) / len(results)
        relevance_factor = min(avg_relevance * 2, 1.0)
        
        return base_confidence * relevance_factor
    
    def _generate_reasoning(self, results: List[Dict], query: str) -> str:
        """Generate reasoning for analysis results"""
        
        if not results:
            return "No relevant scripts found for the query"
        
        reasoning_parts = [
            f"Found {len(results)} relevant scripts",
            f"Top match: {results[0]['county']} (score: {results[0]['relevance_score']:.2f})"
        ]
        
        # Add pattern information
        counties = set(r['county'] for r in results)
        if len(counties) > 1:
            reasoning_parts.append(f"Implementations found in {len(counties)} counties")
        
        return " | ".join(reasoning_parts)
    
    def _create_analysis_summary(self, results: List[Dict]) -> Dict[str, Any]:
        """Create summary of analysis results"""
        
        if not results:
            return {}
        
        # Count by county
        county_counts = {}
        for result in results:
            county = result['county']
            county_counts[county] = county_counts.get(county, 0) + 1
        
        # Count by event prefix
        event_prefix_counts = {}
        for result in results:
            prefix = result['metadata'].get('event_prefix')
            if prefix:
                event_prefix_counts[prefix] = event_prefix_counts.get(prefix, 0) + 1
        
        return {
            'total_scripts': len(results),
            'counties': county_counts,
            'event_prefixes': event_prefix_counts,
            'avg_relevance': sum(r['relevance_score'] for r in results) / len(results)
        }
    
    def _generate_recommendations(self, results: List[Dict]) -> List[str]:
        """Generate recommendations based on analysis"""
        
        recommendations = []
        
        if not results:
            recommendations.append("No relevant implementations found - consider broader search terms")
            return recommendations
        
        # Recommend top implementation
        top_result = results[0]
        recommendations.append(f"Primary recommendation: {top_result['county']} implementation")
        
        # Recommend based on complexity
        simple_implementations = [r for r in results if r['metadata'].get('complexity') == 'low']
        if simple_implementations:
            recommendations.append(f"Consider {simple_implementations[0]['county']} for simpler implementation")
        
        # Recommend based on documentation
        well_documented = [r for r in results if r['metadata'].get('doc_quality') == 'excellent']
        if well_documented:
            recommendations.append(f"Best documented: {well_documented[0]['county']}")
        
        return recommendations
