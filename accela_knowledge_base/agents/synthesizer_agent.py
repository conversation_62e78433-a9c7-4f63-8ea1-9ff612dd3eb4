"""
Synthesizer agent for creating comprehensive implementation plans
"""

from typing import Dict, Any, List
from ..core.models import Agent<PERSON><PERSON>, AgentResponse, Agent<PERSON><PERSON>
from .base_agent import BaseAgent


class SynthesizerAgent(BaseAgent):
    """Agent responsible for synthesizing results into actionable plans"""
    
    def __init__(self, knowledge_graph, llm_helper=None):
        super().__init__("SynthesizerAgent", AgentRole.SYNTHESIZER, llm_helper)
        self.knowledge_graph = knowledge_graph
    
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """
        Execute synthesis task
        
        Args:
            task: Synthesis task to execute
            
        Returns:
            AgentResponse with synthesis results
        """
        
        self.logger.info(f"Executing synthesis task: {task.query}")
        
        try:
            # Get all previous results from context
            analysis_results = task.context.get('analysis_results', {})
            comparison_results = task.context.get('comparison_results', {})
            recommendation_results = task.context.get('recommendation_results', {})
            
            # Create comprehensive synthesis
            synthesis = self._create_synthesis(
                analysis_results, 
                comparison_results, 
                recommendation_results, 
                task.query
            )
            
            # Calculate confidence
            confidence = self._calculate_confidence(synthesis)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(synthesis)
            
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result=synthesis,
                confidence=confidence,
                reasoning=reasoning,
                recommendations=synthesis.get('action_items', [])
            )
            
        except Exception as e:
            self.logger.error(f"Synthesis task failed: {e}")
            return AgentResponse(
                task_id=task.task_id,
                agent_role=self.role,
                result={'error': str(e)},
                confidence=0.0,
                reasoning=f"Synthesis failed: {str(e)}",
                recommendations=[]
            )
    
    def _create_synthesis(self, analysis: Dict, comparison: Dict, recommendation: Dict, query: str) -> Dict[str, Any]:
        """Create comprehensive synthesis from all agent results"""
        
        synthesis = {
            'query': query,
            'executive_summary': self._create_executive_summary(analysis, recommendation),
            'optimal_approach': self._determine_optimal_approach(recommendation),
            'implementation_plan': self._create_implementation_plan(recommendation, analysis),
            'risk_assessment': self._assess_risks(analysis, comparison, recommendation),
            'resource_requirements': self._estimate_resources(recommendation),
            'success_metrics': self._define_success_metrics(query, recommendation),
            'action_items': self._generate_action_items(recommendation, analysis)
        }
        
        return synthesis
    
    def _create_executive_summary(self, analysis: Dict, recommendation: Dict) -> str:
        """Create executive summary of findings"""
        
        summary_parts = []
        
        # Analysis summary
        if analysis.get('result', {}).get('total_found', 0) > 0:
            total_scripts = analysis['result']['total_found']
            summary_parts.append(f"Found {total_scripts} relevant implementations")
        
        # Recommendation summary
        best_impl = recommendation.get('result', {}).get('best_implementation')
        if best_impl:
            county = best_impl.get('county', 'unknown')
            score = best_impl.get('recommendation_score', 0)
            summary_parts.append(f"Primary recommendation: {county} (score: {score:.2f})")
        
        # Confidence assessment
        avg_confidence = (
            analysis.get('confidence', 0) + 
            recommendation.get('confidence', 0)
        ) / 2
        
        if avg_confidence > 0.8:
            summary_parts.append("High confidence in recommendations")
        elif avg_confidence > 0.6:
            summary_parts.append("Moderate confidence in recommendations")
        else:
            summary_parts.append("Low confidence - additional analysis recommended")
        
        return ". ".join(summary_parts) + "."
    
    def _determine_optimal_approach(self, recommendation: Dict) -> Dict[str, Any]:
        """Determine the optimal implementation approach"""
        
        best_impl = recommendation.get('result', {}).get('best_implementation')
        alternatives = recommendation.get('result', {}).get('alternatives', [])
        
        if not best_impl:
            return {'approach': 'custom_development', 'reason': 'No suitable existing implementations found'}
        
        approach = {
            'primary_county': best_impl.get('county'),
            'primary_score': best_impl.get('recommendation_score', 0),
            'key_functions': best_impl.get('metadata', {}).get('functions', [])[:5],
            'complexity': best_impl.get('metadata', {}).get('complexity', 'medium'),
            'event_pattern': best_impl.get('metadata', {}).get('event_prefix'),
            'strengths': best_impl.get('strengths', []),
            'considerations': best_impl.get('considerations', [])
        }
        
        # Add hybrid approach if multiple good options
        if len(alternatives) > 0 and alternatives[0].get('recommendation_score', 0) > 0.7:
            approach['hybrid_option'] = {
                'secondary_county': alternatives[0].get('county'),
                'secondary_score': alternatives[0].get('recommendation_score', 0),
                'rationale': 'Consider combining approaches for comprehensive solution'
            }
        
        return approach
    
    def _create_implementation_plan(self, recommendation: Dict, analysis: Dict) -> List[Dict[str, Any]]:
        """Create phased implementation plan"""
        
        best_impl = recommendation.get('result', {}).get('best_implementation')
        
        if not best_impl:
            return [
                {
                    'phase': 1,
                    'title': 'Requirements Analysis',
                    'duration': '1-2 weeks',
                    'tasks': ['Define detailed requirements', 'Research alternative solutions'],
                    'deliverables': ['Requirements document', 'Technical specification']
                }
            ]
        
        county = best_impl.get('county', 'unknown')
        complexity = best_impl.get('metadata', {}).get('complexity', 'medium')
        functions = best_impl.get('metadata', {}).get('functions', [])
        
        # Adjust plan based on complexity
        if complexity == 'low':
            duration_factor = 1.0
        elif complexity == 'high':
            duration_factor = 1.5
        else:
            duration_factor = 1.2
        
        plan = [
            {
                'phase': 1,
                'title': 'Analysis and Setup',
                'duration': f'{int(1 * duration_factor)} week{"s" if duration_factor > 1 else ""}',
                'tasks': [
                    f'Analyze {county} implementation in detail',
                    'Setup development environment',
                    'Review Accela configuration requirements'
                ],
                'deliverables': ['Implementation analysis', 'Development environment', 'Configuration checklist']
            },
            {
                'phase': 2,
                'title': 'Core Implementation',
                'duration': f'{int(2 * duration_factor)} weeks',
                'tasks': [
                    f'Implement core functions: {", ".join(functions[:3])}',
                    'Configure event handlers and workflows',
                    'Implement error handling and logging'
                ],
                'deliverables': ['Core functionality', 'Event configuration', 'Error handling']
            },
            {
                'phase': 3,
                'title': 'Testing and Refinement',
                'duration': f'{int(1 * duration_factor)} week{"s" if duration_factor > 1 else ""}',
                'tasks': [
                    'Unit testing of all functions',
                    'Integration testing with Accela',
                    'Performance optimization'
                ],
                'deliverables': ['Test suite', 'Integration verification', 'Performance report']
            },
            {
                'phase': 4,
                'title': 'Deployment and Documentation',
                'duration': '1 week',
                'tasks': [
                    'Deploy to staging environment',
                    'Create user documentation',
                    'Conduct user training'
                ],
                'deliverables': ['Staging deployment', 'Documentation', 'Training materials']
            }
        ]
        
        return plan
    
    def _assess_risks(self, analysis: Dict, comparison: Dict, recommendation: Dict) -> List[Dict[str, Any]]:
        """Assess implementation risks"""
        
        risks = []
        
        # Low confidence risk
        avg_confidence = (
            analysis.get('confidence', 0) + 
            recommendation.get('confidence', 0)
        ) / 2
        
        if avg_confidence < 0.6:
            risks.append({
                'risk': 'Low confidence in recommendations',
                'impact': 'High',
                'probability': 'Medium',
                'mitigation': 'Conduct additional analysis and prototyping'
            })
        
        # Complexity risk
        best_impl = recommendation.get('result', {}).get('best_implementation')
        if best_impl and best_impl.get('metadata', {}).get('complexity') == 'high':
            risks.append({
                'risk': 'High implementation complexity',
                'impact': 'Medium',
                'probability': 'High',
                'mitigation': 'Allocate additional development time and expertise'
            })
        
        # Documentation risk
        if best_impl and best_impl.get('metadata', {}).get('doc_quality') == 'poor':
            risks.append({
                'risk': 'Limited documentation available',
                'impact': 'Medium',
                'probability': 'High',
                'mitigation': 'Plan for additional code analysis and reverse engineering'
            })
        
        # Limited alternatives risk
        alternatives = recommendation.get('result', {}).get('alternatives', [])
        if len(alternatives) < 2:
            risks.append({
                'risk': 'Limited alternative implementations',
                'impact': 'Medium',
                'probability': 'Low',
                'mitigation': 'Develop fallback custom implementation plan'
            })
        
        return risks
    
    def _estimate_resources(self, recommendation: Dict) -> Dict[str, Any]:
        """Estimate resource requirements"""
        
        best_impl = recommendation.get('result', {}).get('best_implementation')
        
        if not best_impl:
            return {
                'development_time': '4-6 weeks',
                'team_size': '2-3 developers',
                'skills_required': ['Accela development', 'JavaScript', 'System integration']
            }
        
        complexity = best_impl.get('metadata', {}).get('complexity', 'medium')
        function_count = len(best_impl.get('metadata', {}).get('functions', []))
        
        # Estimate based on complexity and scope
        if complexity == 'low' and function_count <= 3:
            dev_time = '2-3 weeks'
            team_size = '1-2 developers'
        elif complexity == 'high' or function_count > 8:
            dev_time = '6-8 weeks'
            team_size = '3-4 developers'
        else:
            dev_time = '4-5 weeks'
            team_size = '2-3 developers'
        
        return {
            'development_time': dev_time,
            'team_size': team_size,
            'skills_required': [
                'Accela development experience',
                'JavaScript programming',
                'System integration',
                'Database management'
            ],
            'estimated_effort': f'{function_count * 2}-{function_count * 3} person-days'
        }
    
    def _define_success_metrics(self, query: str, recommendation: Dict) -> List[str]:
        """Define success metrics for the implementation"""
        
        metrics = [
            'Successful deployment to production environment',
            'All core functions working as expected',
            'Integration with existing Accela workflows',
            'User acceptance and training completion'
        ]
        
        # Add query-specific metrics
        query_lower = query.lower()
        if 'email' in query_lower:
            metrics.append('Email notifications sent successfully')
        if 'workflow' in query_lower:
            metrics.append('Workflow automation functioning correctly')
        if 'inspection' in query_lower:
            metrics.append('Inspection processes streamlined')
        
        return metrics
    
    def _generate_action_items(self, recommendation: Dict, analysis: Dict) -> List[str]:
        """Generate immediate action items"""
        
        actions = []
        
        best_impl = recommendation.get('result', {}).get('best_implementation')
        
        if best_impl:
            county = best_impl.get('county')
            actions.append(f"Review {county} implementation in detail")
            actions.append(f"Contact {county} team for implementation insights")
            
            if best_impl.get('metadata', {}).get('doc_quality') == 'poor':
                actions.append("Conduct thorough code analysis due to limited documentation")
        else:
            actions.append("Develop custom implementation plan")
            actions.append("Research additional implementation options")
        
        # General actions
        actions.extend([
            "Setup development and testing environments",
            "Define detailed technical requirements",
            "Create project timeline and resource allocation"
        ])
        
        return actions
    
    def _calculate_confidence(self, synthesis: Dict) -> float:
        """Calculate confidence in synthesis"""
        
        # Base confidence on completeness of synthesis
        completeness_score = 0.0
        
        if synthesis.get('optimal_approach'):
            completeness_score += 0.3
        if synthesis.get('implementation_plan'):
            completeness_score += 0.3
        if synthesis.get('risk_assessment'):
            completeness_score += 0.2
        if synthesis.get('action_items'):
            completeness_score += 0.2
        
        return completeness_score
    
    def _generate_reasoning(self, synthesis: Dict) -> str:
        """Generate reasoning for synthesis"""
        
        reasoning_parts = []
        
        if synthesis.get('optimal_approach', {}).get('primary_county'):
            county = synthesis['optimal_approach']['primary_county']
            reasoning_parts.append(f"Primary approach: {county} implementation")
        
        plan_phases = len(synthesis.get('implementation_plan', []))
        if plan_phases > 0:
            reasoning_parts.append(f"Implementation plan: {plan_phases} phases")
        
        risk_count = len(synthesis.get('risk_assessment', []))
        if risk_count > 0:
            reasoning_parts.append(f"Identified {risk_count} risks with mitigations")
        
        return " | ".join(reasoning_parts) if reasoning_parts else "Comprehensive synthesis completed"
