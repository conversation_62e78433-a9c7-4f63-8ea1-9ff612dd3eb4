"""
Base agent class for all intelligent agents
"""

from abc import ABC, abstractmethod
from typing import Any, Optional
from ..core.models import Agent<PERSON><PERSON>, AgentResponse, AgentRole
from ..core.logging import LoggerMixin
from ..llm.llm_helper import LLMHelper


class BaseAgent(ABC, LoggerMixin):
    """Base class for all intelligent agents"""
    
    def __init__(self, name: str, role: Agent<PERSON><PERSON>, llm_helper: Optional[LLMHelper] = None):
        self.name = name
        self.role = role
        self.memory = {}  # Agent's working memory
        self.llm_helper = llm_helper
        
    @abstractmethod
    def execute_task(self, task: AgentTask) -> AgentResponse:
        """Execute a specific task"""
        pass
    
    def update_memory(self, key: str, value: Any) -> None:
        """Update agent's memory"""
        self.memory[key] = value
        self.logger.debug(f"Updated memory: {key}")
    
    def get_memory(self, key: str, default: Any = None) -> Any:
        """Get value from agent's memory"""
        return self.memory.get(key, default)
    
    def clear_memory(self) -> None:
        """Clear agent's memory"""
        self.memory.clear()
        self.logger.debug("Memory cleared")
    
    def has_llm_capability(self) -> bool:
        """Check if agent has LLM capability"""
        return self.llm_helper is not None and self.llm_helper.is_available()
    
    def __str__(self) -> str:
        """String representation of agent"""
        return f"{self.name}({self.role.value})"
    
    def __repr__(self) -> str:
        """Detailed representation of agent"""
        return f"<{self.__class__.__name__}: {self.name}, role={self.role.value}, llm={self.has_llm_capability()}>"
