"""
Test runner for Accela Knowledge Base
"""

from typing import Dict, List
from ..core.config import Config
from ..core.logging import LoggerMixin


class TestRunner(LoggerMixin):
    """Runs tests for the Accela Knowledge Base system"""
    
    def __init__(self, config: Config):
        self.config = config
    
    def run_all(self) -> Dict[str, any]:
        """Run all tests"""
        
        self.logger.info("Running all tests")
        
        results = {
            'success': True,
            'failures': [],
            'tests_run': 0,
            'tests_passed': 0
        }
        
        # Test configuration
        config_result = self._test_configuration()
        results['tests_run'] += 1
        if config_result['success']:
            results['tests_passed'] += 1
        else:
            results['success'] = False
            results['failures'].append(config_result['error'])
        
        # Test metadata extraction
        metadata_result = self._test_metadata_extraction()
        results['tests_run'] += 1
        if metadata_result['success']:
            results['tests_passed'] += 1
        else:
            results['success'] = False
            results['failures'].append(metadata_result['error'])
        
        # Test knowledge graph
        graph_result = self._test_knowledge_graph()
        results['tests_run'] += 1
        if graph_result['success']:
            results['tests_passed'] += 1
        else:
            results['success'] = False
            results['failures'].append(graph_result['error'])
        
        self.logger.info(f"Tests completed: {results['tests_passed']}/{results['tests_run']} passed")
        
        return results
    
    def _test_configuration(self) -> Dict[str, any]:
        """Test configuration validation"""
        try:
            self.config.validate()
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': f"Configuration test failed: {e}"}
    
    def _test_metadata_extraction(self) -> Dict[str, any]:
        """Test metadata extraction"""
        try:
            from ..data.metadata_extractor import MetadataExtractor
            
            extractor = MetadataExtractor(self.config)
            
            # Test loading existing metadata
            metadata = extractor.load_metadata()
            
            if not metadata:
                return {'success': False, 'error': "No metadata found - run extract-metadata first"}
            
            # Basic validation
            if not isinstance(metadata, list):
                return {'success': False, 'error': "Metadata should be a list"}
            
            if len(metadata) == 0:
                return {'success': False, 'error': "Metadata list is empty"}
            
            # Check first item structure
            first_item = metadata[0]
            required_fields = ['file_path', 'county', 'script_type']
            
            for field in required_fields:
                if field not in first_item:
                    return {'success': False, 'error': f"Missing required field: {field}"}
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': f"Metadata extraction test failed: {e}"}
    
    def _test_knowledge_graph(self) -> Dict[str, any]:
        """Test knowledge graph building"""
        try:
            from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
            
            graph = AccelaKnowledgeGraph(self.config)
            
            # Test loading metadata and building graph
            graph.build_from_metadata()
            
            # Check graph stats
            stats = graph.get_stats()
            
            if stats['nodes'] == 0:
                return {'success': False, 'error': "Knowledge graph has no nodes"}
            
            if stats['counties'] == 0:
                return {'success': False, 'error': "Knowledge graph has no counties"}
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': f"Knowledge graph test failed: {e}"}
