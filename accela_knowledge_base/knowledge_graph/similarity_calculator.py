"""
Similarity calculator for knowledge graph nodes
"""

from typing import Dict, List, Set, Any
from ..core.logging import LoggerMixin


class SimilarityCalculator(LoggerMixin):
    """Calculates similarity between knowledge graph nodes"""
    
    def __init__(self):
        pass
    
    def calculate_jaccard_similarity(self, set1: Set, set2: Set) -> float:
        """
        Calculate Jaccard similarity between two sets
        
        Args:
            set1: First set
            set2: Second set
            
        Returns:
            Jaccard similarity score (0.0 to 1.0)
        """
        if not set1 and not set2:
            return 1.0
        
        if not set1 or not set2:
            return 0.0
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def calculate_function_similarity(self, functions1: List[str], functions2: List[str]) -> float:
        """
        Calculate similarity between two lists of functions
        
        Args:
            functions1: First list of functions
            functions2: Second list of functions
            
        Returns:
            Function similarity score (0.0 to 1.0)
        """
        set1 = set(functions1)
        set2 = set(functions2)
        
        return self.calculate_jaccard_similarity(set1, set2)
    
    def calculate_county_similarity(self, county1_data: Dict[str, Any], county2_data: Dict[str, Any]) -> float:
        """
        Calculate similarity between two counties based on their implementations
        
        Args:
            county1_data: Data for first county
            county2_data: Data for second county
            
        Returns:
            County similarity score (0.0 to 1.0)
        """
        
        # Extract component sets
        county1_components = self._extract_county_components(county1_data)
        county2_components = self._extract_county_components(county2_data)
        
        # Calculate similarities for different components
        similarities = []
        weights = []
        
        # Event prefixes similarity
        event_sim = self.calculate_jaccard_similarity(
            county1_components.get('event_prefixes', set()),
            county2_components.get('event_prefixes', set())
        )
        similarities.append(event_sim)
        weights.append(0.3)
        
        # Modules similarity
        module_sim = self.calculate_jaccard_similarity(
            county1_components.get('modules', set()),
            county2_components.get('modules', set())
        )
        similarities.append(module_sim)
        weights.append(0.25)
        
        # Application types similarity
        app_type_sim = self.calculate_jaccard_similarity(
            county1_components.get('app_types', set()),
            county2_components.get('app_types', set())
        )
        similarities.append(app_type_sim)
        weights.append(0.25)
        
        # Functions similarity
        func_sim = self.calculate_jaccard_similarity(
            county1_components.get('functions', set()),
            county2_components.get('functions', set())
        )
        similarities.append(func_sim)
        weights.append(0.2)
        
        # Calculate weighted average
        if similarities:
            weighted_sum = sum(s * w for s, w in zip(similarities, weights))
            total_weight = sum(weights)
            return weighted_sum / total_weight if total_weight > 0 else 0.0
        
        return 0.0
    
    def _extract_county_components(self, county_data: Dict[str, Any]) -> Dict[str, Set]:
        """
        Extract component sets from county data
        
        Args:
            county_data: County data dictionary
            
        Returns:
            Dictionary with component sets
        """
        components = {
            'event_prefixes': set(),
            'modules': set(),
            'app_types': set(),
            'functions': set()
        }
        
        # Extract from scripts if available
        scripts = county_data.get('scripts', [])
        for script in scripts:
            if isinstance(script, dict):
                # Event prefixes
                event_prefix = script.get('event_prefix')
                if event_prefix:
                    components['event_prefixes'].add(event_prefix)
                
                # Modules
                module = script.get('module')
                if module:
                    components['modules'].add(module)
                
                # Application types
                app_type = script.get('application_type')
                if app_type:
                    components['app_types'].add(app_type)
                
                # Functions
                functions = script.get('functions', [])
                for func in functions:
                    components['functions'].add(func)
        
        return components
