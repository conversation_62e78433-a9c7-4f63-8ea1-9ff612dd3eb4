"""
Graph analyzer for knowledge graph analysis and querying
"""

from typing import Dict, List, Any, Optional
import networkx as nx
from ..core.logging import LoggerMixin


class GraphAnalyzer(LoggerMixin):
    """Analyzes knowledge graph for patterns and insights"""
    
    def __init__(self, graph: nx.MultiDiGraph):
        self.graph = graph
    
    def analyze_centrality(self) -> Dict[str, Any]:
        """
        Analyze node centrality in the graph
        
        Returns:
            Dictionary with centrality analysis
        """
        
        try:
            # Calculate different centrality measures
            degree_centrality = nx.degree_centrality(self.graph)
            betweenness_centrality = nx.betweenness_centrality(self.graph)
            closeness_centrality = nx.closeness_centrality(self.graph)
            
            # Find top nodes for each centrality measure
            top_degree = sorted(degree_centrality.items(), key=lambda x: x[1], reverse=True)[:10]
            top_betweenness = sorted(betweenness_centrality.items(), key=lambda x: x[1], reverse=True)[:10]
            top_closeness = sorted(closeness_centrality.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                'degree_centrality': {
                    'top_nodes': top_degree,
                    'average': sum(degree_centrality.values()) / len(degree_centrality)
                },
                'betweenness_centrality': {
                    'top_nodes': top_betweenness,
                    'average': sum(betweenness_centrality.values()) / len(betweenness_centrality)
                },
                'closeness_centrality': {
                    'top_nodes': top_closeness,
                    'average': sum(closeness_centrality.values()) / len(closeness_centrality)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Centrality analysis failed: {e}")
            return {}
    
    def find_communities(self) -> Dict[str, Any]:
        """
        Find communities in the graph
        
        Returns:
            Dictionary with community analysis
        """
        
        try:
            # Convert to undirected graph for community detection
            undirected_graph = self.graph.to_undirected()
            
            # Use simple community detection (connected components)
            communities = list(nx.connected_components(undirected_graph))
            
            # Analyze communities
            community_analysis = {
                'total_communities': len(communities),
                'communities': []
            }
            
            for i, community in enumerate(communities):
                community_info = {
                    'id': i,
                    'size': len(community),
                    'nodes': list(community)[:10],  # Limit to first 10 nodes
                    'node_types': {}
                }
                
                # Analyze node types in community
                for node in community:
                    node_type = self.graph.nodes[node].get('type', 'unknown')
                    community_info['node_types'][node_type] = community_info['node_types'].get(node_type, 0) + 1
                
                community_analysis['communities'].append(community_info)
            
            return community_analysis
            
        except Exception as e:
            self.logger.error(f"Community detection failed: {e}")
            return {}
    
    def analyze_patterns(self) -> Dict[str, Any]:
        """
        Analyze patterns in the graph
        
        Returns:
            Dictionary with pattern analysis
        """
        
        patterns = {
            'node_type_distribution': {},
            'edge_type_distribution': {},
            'most_connected_nodes': [],
            'isolated_nodes': []
        }
        
        try:
            # Analyze node types
            for node in self.graph.nodes():
                node_type = self.graph.nodes[node].get('type', 'unknown')
                patterns['node_type_distribution'][node_type] = patterns['node_type_distribution'].get(node_type, 0) + 1
            
            # Analyze edge types
            for edge in self.graph.edges(data=True):
                edge_type = edge[2].get('relationship', 'unknown')
                patterns['edge_type_distribution'][edge_type] = patterns['edge_type_distribution'].get(edge_type, 0) + 1
            
            # Find most connected nodes
            node_degrees = [(node, self.graph.degree(node)) for node in self.graph.nodes()]
            patterns['most_connected_nodes'] = sorted(node_degrees, key=lambda x: x[1], reverse=True)[:10]
            
            # Find isolated nodes
            patterns['isolated_nodes'] = [node for node in self.graph.nodes() if self.graph.degree(node) == 0]
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
        
        return patterns
    
    def find_shortest_paths(self, source: str, target: str) -> Optional[List[str]]:
        """
        Find shortest path between two nodes
        
        Args:
            source: Source node
            target: Target node
            
        Returns:
            Shortest path as list of nodes, or None if no path exists
        """
        
        try:
            if source not in self.graph or target not in self.graph:
                return None
            
            return nx.shortest_path(self.graph, source, target)
            
        except nx.NetworkXNoPath:
            return None
        except Exception as e:
            self.logger.error(f"Shortest path calculation failed: {e}")
            return None
    
    def get_node_neighbors(self, node: str, max_distance: int = 1) -> Dict[str, Any]:
        """
        Get neighbors of a node within specified distance
        
        Args:
            node: Node to analyze
            max_distance: Maximum distance to search
            
        Returns:
            Dictionary with neighbor information
        """
        
        if node not in self.graph:
            return {}
        
        neighbors = {
            'direct_neighbors': [],
            'neighbors_by_distance': {}
        }
        
        try:
            # Direct neighbors
            direct = list(self.graph.neighbors(node))
            neighbors['direct_neighbors'] = direct
            
            # Neighbors by distance
            for distance in range(1, max_distance + 1):
                distance_neighbors = []
                
                # Use BFS to find nodes at specific distance
                visited = set()
                current_level = {node}
                
                for _ in range(distance):
                    next_level = set()
                    for current_node in current_level:
                        if current_node not in visited:
                            visited.add(current_node)
                            next_level.update(self.graph.neighbors(current_node))
                    current_level = next_level - visited
                
                neighbors['neighbors_by_distance'][distance] = list(current_level)
            
        except Exception as e:
            self.logger.error(f"Neighbor analysis failed: {e}")
        
        return neighbors
