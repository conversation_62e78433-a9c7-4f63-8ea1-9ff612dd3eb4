"""
Agentic endpoints for multi-agent orchestration
"""

from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uuid
from datetime import datetime

from ...core.models import OrchestrationRequest
from ...core.logging import get_logger

router = APIRouter()
logger = get_logger("agentic")


class AgenticQueryRequest(BaseModel):
    query: str
    use_case: str
    target_counties: Optional[List[str]] = None
    constraints: Dict[str, Any] = {}
    priority: int = 1


class AgenticQueryResponse(BaseModel):
    request_id: str
    query: str
    best_implementation: Dict[str, Any]
    alternatives: List[Dict[str, Any]]
    synthesis: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str]
    processing_time: float


@router.post("/query", response_model=AgenticQueryResponse)
async def agentic_query(request: AgenticQueryRequest, app_request: Request):
    """Process query using multi-agent orchestration"""
    
    orchestrator = getattr(app_request.app.state, 'orchestrator', None)
    if not orchestrator:
        raise HTTPException(status_code=500, detail="Orchestrator not initialized")
    
    start_time = datetime.now()
    
    # Create orchestration request
    orchestration_request = OrchestrationRequest(
        request_id=str(uuid.uuid4()),
        query=request.query,
        use_case=request.use_case,
        target_counties=request.target_counties,
        constraints=request.constraints,
        priority=request.priority
    )
    
    try:
        # Execute orchestration
        result = await orchestrator.orchestrate(orchestration_request)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return AgenticQueryResponse(
            request_id=result.request_id,
            query=result.query,
            best_implementation=result.best_implementation,
            alternatives=result.alternatives,
            synthesis=result.synthesis,
            confidence=result.confidence,
            reasoning=result.reasoning,
            recommendations=result.recommendations,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Agentic query failed: {e}")
        raise HTTPException(status_code=500, detail=f"Orchestration failed: {str(e)}")


@router.get("/status")
async def get_agentic_status(request: Request):
    """Get status of the agentic system"""
    
    try:
        orchestrator = getattr(request.app.state, 'orchestrator', None)
        knowledge_graph = getattr(request.app.state, 'knowledge_graph', None)
        
        if not orchestrator or not knowledge_graph:
            return {"status": "not_ready", "reason": "System not initialized"}
        
        # Get graph stats
        graph_stats = knowledge_graph.get_stats()
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat(),
            "graph_stats": graph_stats,
            "agents": {
                "analyzer": "ready",
                "comparator": "ready", 
                "recommender": "ready",
                "synthesizer": "ready"
            },
            "llm_enabled": request.app.state.config.llm_enabled
        }
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")
