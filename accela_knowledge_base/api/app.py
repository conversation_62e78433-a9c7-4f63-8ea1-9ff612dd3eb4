"""
FastAPI application factory
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from ..core.config import Config
from ..core.logging import setup_logging, get_logger
from .endpoints import agentic, accela, graph, health


def create_app(config: Config = None) -> FastAPI:
    """
    Create and configure FastAPI application
    
    Args:
        config: Configuration object
        
    Returns:
        Configured FastAPI application
    """
    
    if config is None:
        config = Config.from_env()
    
    # Setup logging
    setup_logging()
    logger = get_logger("api")
    
    # Validate configuration
    try:
        config.validate()
        logger.info(f"Configuration validated: {config}")
    except ValueError as e:
        logger.error(f"Invalid configuration: {e}")
        raise
    
    # Create FastAPI app
    app = FastAPI(
        title="Accela Agentic Knowledge Base API",
        description="Multi-agent system for optimal Accela implementation recommendations",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Store config in app state
    app.state.config = config
    
    # Include routers
    app.include_router(health.router, prefix="/health", tags=["health"])
    app.include_router(agentic.router, prefix="/agentic", tags=["agentic"])
    app.include_router(accela.router, prefix="/accela", tags=["accela"])
    app.include_router(graph.router, prefix="/graph", tags=["graph"])
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize system on startup"""
        logger.info("Starting Accela Knowledge Base API")
        
        # Initialize knowledge graph and orchestrator
        try:
            from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
            from ..agents.orchestrator import MultiAgentOrchestrator
            
            # Build knowledge graph
            knowledge_graph = AccelaKnowledgeGraph(config)
            knowledge_graph.build_from_metadata()
            
            # Initialize orchestrator
            orchestrator = MultiAgentOrchestrator(knowledge_graph, config)
            
            # Store in app state
            app.state.knowledge_graph = knowledge_graph
            app.state.orchestrator = orchestrator
            
            logger.info("System initialized successfully")
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            raise
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup on shutdown"""
        logger.info("Shutting down Accela Knowledge Base API")
    
    return app
