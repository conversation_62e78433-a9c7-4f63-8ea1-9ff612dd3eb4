"""
Custom exceptions for Accela Knowledge Base
"""


class AccelaKnowledgeBaseError(Exception):
    """Base exception for Accela Knowledge Base"""
    pass


class GraphBuildError(AccelaKnowledgeBaseError):
    """Exception raised when graph building fails"""
    pass


class AgentError(AccelaKnowledgeBaseError):
    """Exception raised when agent execution fails"""
    pass


class MetadataExtractionError(AccelaKnowledgeBaseError):
    """Exception raised when metadata extraction fails"""
    pass


class LLMError(AccelaKnowledgeBaseError):
    """Exception raised when LLM operations fail"""
    pass


class ConfigurationError(AccelaKnowledgeBaseError):
    """Exception raised when configuration is invalid"""
    pass


class APIError(AccelaKnowledgeBaseError):
    """Exception raised when API operations fail"""
    pass
