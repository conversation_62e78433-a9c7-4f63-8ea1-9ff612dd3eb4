"""
Configuration management for Accela Knowledge Base
"""

import os
from typing import Dict, Optional
from dataclasses import dataclass, field
from pathlib import Path

# Try to load python-dotenv for .env file support
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False


@dataclass
class Config:
    """Production configuration for Accela Knowledge Base"""
    
    # Data paths
    src_directory: str = "src"
    metadata_file: str = "accela_metadata.json"
    graph_export_file: str = "accela_knowledge_graph.gexf"
    
    # API configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    api_workers: int = 4
    api_secret_key: str = "dev-secret-key-change-in-production"
    api_cors_origins: str = "*"
    
    # LLM configuration
    openai_api_key: Optional[str] = field(default_factory=lambda: os.getenv('OPENAI_API_KEY'))
    llm_model: str = "gpt-3.5-turbo"
    llm_max_tokens_analysis: int = 300
    llm_max_tokens_reasoning: int = 150
    llm_temperature: float = 0.1
    
    # Agent configuration
    analysis_relevance_threshold: int = 2
    similarity_threshold: float = 0.3
    function_similarity_threshold: float = 0.4
    
    # Performance settings
    max_code_length_for_llm: int = 2000
    max_search_results: int = 20
    cache_enabled: bool = True

    # Database configuration
    database_url: Optional[str] = None
    redis_url: Optional[str] = None

    # Monitoring and observability
    metrics_enabled: bool = False
    health_check_interval: int = 30
    prometheus_port: Optional[int] = None

    # Security configuration
    jwt_secret_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24
    rate_limit_enabled: bool = False
    rate_limit_requests_per_minute: int = 60

    # Email configuration
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_use_tls: bool = True

    # Database configuration (for future enhancements)
    database_url: Optional[str] = None
    redis_url: Optional[str] = None

    # Monitoring and observability
    metrics_enabled: bool = False
    health_check_interval: int = 30
    prometheus_port: Optional[int] = None

    # Email configuration (for notifications)
    smtp_host: Optional[str] = None
    smtp_port: int = 587
    smtp_user: Optional[str] = None
    smtp_password: Optional[str] = None
    smtp_use_tls: bool = True

    # Security settings
    jwt_secret_key: Optional[str] = None
    jwt_algorithm: str = "HS256"
    jwt_expiration_hours: int = 24

    # Rate limiting
    rate_limit_enabled: bool = False
    rate_limit_requests_per_minute: int = 60

    # Development and testing
    debug: bool = False
    reload: bool = False
    test_mode: bool = False
    
    # County mappings
    county_mappings: Dict[str, str] = field(default_factory=lambda: {
        "accela-masterscripts": "asheville",
        "Accela": "santa_barbara", 
        "AccelaProd": "dayton",
        "LeonAccelaScripts": "leon",
        "MyAccela": "atlanta_chattanooga",
        "AccelaSupp": "lancaster",
        "COKApplications-Accela": "cok",
        "AccelaDEV": "ep_support",
        "EMSE_DEV_3_0": "solano"
    })
    
    # Event prefix descriptions
    event_prefixes: Dict[str, str] = field(default_factory=lambda: {
        'ASA': 'Application Submittal After',
        'ASB': 'Application Submittal Before', 
        'ASIUA': 'Application Submittal In Use After',
        'ASUA': 'Application Status Update After',
        'ASUB': 'Application Status Update Before',
        'CTRCA': 'Convert To Real Cap After',
        'WTUA': 'Workflow Task Update After',
        'WTUB': 'Workflow Task Update Before',
        'ISA': 'Inspection Schedule After',
        'ISB': 'Inspection Schedule Before',
        'IRSA': 'Inspection Result Submit After',
        'IRSB': 'Inspection Result Submit Before',
        'IFA': 'Inspection Finalize After',
        'PRA': 'Payment Receive After',
        'PIA': 'Payment Invoice After',
        'FAA': 'Fee Assess After',
        'ICA': 'Invoice Create After',
        'LLSA': 'License Lookup Submit After',
        'CAA': 'Condition Approve After',
        'CEA': 'Condition Edit After',
        'CAEC': 'Condition Approve Edit Create',
        'RLPAA': 'Reference Licensed Professional Add After'
    })
    
    @classmethod
    def from_env(cls, env_file: Optional[str] = None) -> 'Config':
        """Create configuration from environment variables and .env file"""

        # Load .env file if available
        if DOTENV_AVAILABLE:
            if env_file:
                load_dotenv(env_file)
            else:
                # Try to load .env from current directory or parent directories
                env_path = Path('.env')
                if env_path.exists():
                    load_dotenv(env_path)
                else:
                    # Try parent directory
                    parent_env = Path('../.env')
                    if parent_env.exists():
                        load_dotenv(parent_env)

        config = cls()

        # Core paths
        config.src_directory = os.getenv('ACCELA_SRC_DIRECTORY', config.src_directory)
        config.metadata_file = os.getenv('ACCELA_METADATA_FILE', config.metadata_file)
        config.graph_export_file = os.getenv('ACCELA_GRAPH_EXPORT_FILE', config.graph_export_file)

        # API configuration
        config.api_host = os.getenv('ACCELA_API_HOST', config.api_host)
        config.api_port = int(os.getenv('ACCELA_API_PORT', str(config.api_port)))
        config.api_workers = int(os.getenv('ACCELA_API_WORKERS', str(config.api_workers)))
        config.api_secret_key = os.getenv('ACCELA_API_SECRET_KEY', config.api_secret_key)
        config.api_cors_origins = os.getenv('ACCELA_API_CORS_ORIGINS', config.api_cors_origins)

        # LLM configuration
        config.openai_api_key = os.getenv('OPENAI_API_KEY', config.openai_api_key)
        config.llm_model = os.getenv('ACCELA_LLM_MODEL', config.llm_model)
        config.llm_max_tokens_analysis = int(os.getenv('ACCELA_LLM_MAX_TOKENS_ANALYSIS', str(config.llm_max_tokens_analysis)))
        config.llm_max_tokens_reasoning = int(os.getenv('ACCELA_LLM_MAX_TOKENS_REASONING', str(config.llm_max_tokens_reasoning)))
        config.llm_temperature = float(os.getenv('ACCELA_LLM_TEMPERATURE', str(config.llm_temperature)))

        # Agent configuration
        config.analysis_relevance_threshold = int(os.getenv('ACCELA_ANALYSIS_RELEVANCE_THRESHOLD', str(config.analysis_relevance_threshold)))
        config.similarity_threshold = float(os.getenv('ACCELA_SIMILARITY_THRESHOLD', str(config.similarity_threshold)))
        config.function_similarity_threshold = float(os.getenv('ACCELA_FUNCTION_SIMILARITY_THRESHOLD', str(config.function_similarity_threshold)))

        # Performance settings
        config.max_code_length_for_llm = int(os.getenv('ACCELA_MAX_CODE_LENGTH_FOR_LLM', str(config.max_code_length_for_llm)))
        config.max_search_results = int(os.getenv('ACCELA_MAX_SEARCH_RESULTS', str(config.max_search_results)))
        config.cache_enabled = os.getenv('ACCELA_CACHE_ENABLED', 'true').lower() == 'true'

        # Database configuration
        config.database_url = os.getenv('ACCELA_DATABASE_URL', config.database_url)
        config.redis_url = os.getenv('ACCELA_REDIS_URL', config.redis_url)

        # Monitoring and observability
        config.metrics_enabled = os.getenv('ACCELA_METRICS_ENABLED', 'false').lower() == 'true'
        config.health_check_interval = int(os.getenv('ACCELA_HEALTH_CHECK_INTERVAL', str(config.health_check_interval)))
        prometheus_port = os.getenv('ACCELA_PROMETHEUS_PORT')
        if prometheus_port:
            config.prometheus_port = int(prometheus_port)

        # Email configuration
        config.smtp_host = os.getenv('ACCELA_SMTP_HOST', config.smtp_host)
        config.smtp_port = int(os.getenv('ACCELA_SMTP_PORT', str(config.smtp_port)))
        config.smtp_user = os.getenv('ACCELA_SMTP_USER', config.smtp_user)
        config.smtp_password = os.getenv('ACCELA_SMTP_PASSWORD', config.smtp_password)
        config.smtp_use_tls = os.getenv('ACCELA_SMTP_USE_TLS', 'true').lower() == 'true'

        # Security settings
        config.jwt_secret_key = os.getenv('ACCELA_JWT_SECRET_KEY', config.jwt_secret_key)
        config.jwt_algorithm = os.getenv('ACCELA_JWT_ALGORITHM', config.jwt_algorithm)
        config.jwt_expiration_hours = int(os.getenv('ACCELA_JWT_EXPIRATION_HOURS', str(config.jwt_expiration_hours)))

        # Rate limiting
        config.rate_limit_enabled = os.getenv('ACCELA_RATE_LIMIT_ENABLED', 'false').lower() == 'true'
        config.rate_limit_requests_per_minute = int(os.getenv('ACCELA_RATE_LIMIT_REQUESTS_PER_MINUTE', str(config.rate_limit_requests_per_minute)))

        # Development and testing
        config.debug = os.getenv('ACCELA_DEBUG', 'false').lower() == 'true'
        config.reload = os.getenv('ACCELA_RELOAD', 'false').lower() == 'true'
        config.test_mode = os.getenv('ACCELA_TEST_MODE', 'false').lower() == 'true'

        return config
    
    @property
    def llm_enabled(self) -> bool:
        """Check if LLM is enabled"""
        return self.openai_api_key is not None
    
    def validate(self) -> None:
        """Validate configuration"""
        if not os.path.exists(self.src_directory):
            raise ValueError(f"Source directory not found: {self.src_directory}")
        
        if self.api_port < 1 or self.api_port > 65535:
            raise ValueError(f"Invalid API port: {self.api_port}")
        
        if self.api_workers < 1:
            raise ValueError(f"Invalid number of workers: {self.api_workers}")
        
        if self.llm_max_tokens_analysis < 50:
            raise ValueError(f"LLM max tokens too low: {self.llm_max_tokens_analysis}")
    
    def __str__(self) -> str:
        """String representation of config"""
        return f"AccelaConfig(api={self.api_host}:{self.api_port}, llm_enabled={self.llm_enabled})"
