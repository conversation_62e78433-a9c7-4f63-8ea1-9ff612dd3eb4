"""
Environment management utilities
Handles environment-specific configuration and validation
"""

import os
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
from .logging import LoggerMixin


class EnvironmentManager(LoggerMixin):
    """Manages environment configuration and setup"""
    
    ENVIRONMENTS = {
        'development': '.env.development',
        'production': '.env.production', 
        'testing': '.env.testing'
    }
    
    def __init__(self):
        self.current_env = self.detect_environment()
    
    def detect_environment(self) -> str:
        """Detect current environment"""
        
        # Check explicit environment variable
        env = os.getenv('ACCELA_ENVIRONMENT', '').lower()
        if env in self.ENVIRONMENTS:
            return env
        
        # Check for common environment indicators
        if os.getenv('ACCELA_TEST_MODE', '').lower() == 'true':
            return 'testing'
        
        if os.getenv('ACCELA_DEBUG', '').lower() == 'true':
            return 'development'
        
        # Check if we're in a production-like environment
        if os.getenv('ACCELA_API_WORKERS', '1') != '1':
            return 'production'
        
        # Default to development
        return 'development'
    
    def setup_environment(self, environment: str = None) -> None:
        """
        Setup environment by copying appropriate .env file
        
        Args:
            environment: Target environment (development, production, testing)
        """
        
        if environment is None:
            environment = self.current_env
        
        if environment not in self.ENVIRONMENTS:
            raise ValueError(f"Unknown environment: {environment}. Valid: {list(self.ENVIRONMENTS.keys())}")
        
        source_file = self.ENVIRONMENTS[environment]
        target_file = '.env'
        
        if not Path(source_file).exists():
            self.logger.warning(f"Environment file not found: {source_file}")
            return
        
        # Backup existing .env if it exists
        if Path(target_file).exists():
            backup_file = f"{target_file}.backup"
            shutil.copy2(target_file, backup_file)
            self.logger.info(f"Backed up existing {target_file} to {backup_file}")
        
        # Copy environment-specific file
        shutil.copy2(source_file, target_file)
        self.logger.info(f"Environment setup complete: {environment} -> {target_file}")
    
    def validate_environment(self) -> Dict[str, Any]:
        """
        Validate current environment configuration
        
        Returns:
            Dictionary with validation results
        """
        
        validation = {
            'environment': self.current_env,
            'valid': True,
            'warnings': [],
            'errors': [],
            'recommendations': []
        }
        
        # Check required directories
        src_dir = os.getenv('ACCELA_SRC_DIRECTORY', 'src')
        if not Path(src_dir).exists():
            validation['errors'].append(f"Source directory not found: {src_dir}")
            validation['valid'] = False
        
        # Check API configuration
        try:
            api_port = int(os.getenv('ACCELA_API_PORT', '8001'))
            if api_port < 1 or api_port > 65535:
                validation['errors'].append(f"Invalid API port: {api_port}")
                validation['valid'] = False
        except ValueError:
            validation['errors'].append("API port must be a number")
            validation['valid'] = False
        
        # Check workers configuration
        try:
            workers = int(os.getenv('ACCELA_API_WORKERS', '1'))
            if workers < 1:
                validation['errors'].append(f"Invalid worker count: {workers}")
                validation['valid'] = False
            elif workers > 8:
                validation['warnings'].append(f"High worker count: {workers} (consider system resources)")
        except ValueError:
            validation['errors'].append("API workers must be a number")
            validation['valid'] = False
        
        # Check LLM configuration
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            if len(openai_key) < 20:
                validation['warnings'].append("OpenAI API key seems too short")
            validation['recommendations'].append("LLM intelligence enabled - ensure API key is valid")
        else:
            validation['recommendations'].append("Set OPENAI_API_KEY for enhanced LLM intelligence (optional)")
        
        # Environment-specific validations
        if self.current_env == 'production':
            self._validate_production(validation)
        elif self.current_env == 'development':
            self._validate_development(validation)
        elif self.current_env == 'testing':
            self._validate_testing(validation)
        
        return validation
    
    def _validate_production(self, validation: Dict[str, Any]) -> None:
        """Production-specific validation"""
        
        # Check secret key
        secret_key = os.getenv('ACCELA_API_SECRET_KEY', '')
        if 'dev' in secret_key.lower() or 'test' in secret_key.lower():
            validation['errors'].append("Production secret key should not contain 'dev' or 'test'")
            validation['valid'] = False
        
        if len(secret_key) < 32:
            validation['errors'].append("Production secret key should be at least 32 characters")
            validation['valid'] = False
        
        # Check CORS origins
        cors_origins = os.getenv('ACCELA_API_CORS_ORIGINS', '*')
        if cors_origins == '*':
            validation['warnings'].append("CORS origins set to '*' - consider restricting in production")
        
        # Check debug mode
        if os.getenv('ACCELA_DEBUG', 'false').lower() == 'true':
            validation['warnings'].append("Debug mode enabled in production")
        
        # Check logging
        log_level = os.getenv('ACCELA_LOG_LEVEL', 'INFO')
        if log_level.upper() == 'DEBUG':
            validation['warnings'].append("Debug logging enabled in production")
    
    def _validate_development(self, validation: Dict[str, Any]) -> None:
        """Development-specific validation"""
        
        # Check if using production-like settings
        workers = int(os.getenv('ACCELA_API_WORKERS', '1'))
        if workers > 2:
            validation['recommendations'].append("Consider reducing workers for development")
        
        # Check cache settings
        cache_enabled = os.getenv('ACCELA_CACHE_ENABLED', 'false').lower() == 'true'
        if cache_enabled:
            validation['recommendations'].append("Consider disabling cache for development")
    
    def _validate_testing(self, validation: Dict[str, Any]) -> None:
        """Testing-specific validation"""
        
        # Check test mode
        test_mode = os.getenv('ACCELA_TEST_MODE', 'false').lower() == 'true'
        if not test_mode:
            validation['warnings'].append("Test mode not enabled in testing environment")
        
        # Check LLM usage in tests
        openai_key = os.getenv('OPENAI_API_KEY')
        if openai_key:
            validation['warnings'].append("LLM enabled in testing - may cause flaky tests")
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get comprehensive environment information"""
        
        return {
            'current_environment': self.current_env,
            'available_environments': list(self.ENVIRONMENTS.keys()),
            'env_files': {
                env: Path(file).exists() 
                for env, file in self.ENVIRONMENTS.items()
            },
            'active_env_file': '.env' if Path('.env').exists() else None,
            'validation': self.validate_environment()
        }
    
    def create_logs_directory(self) -> None:
        """Create logs directory if it doesn't exist"""
        
        log_file = os.getenv('ACCELA_LOG_FILE', 'logs/accela_kb.log')
        log_dir = Path(log_file).parent
        
        if not log_dir.exists():
            log_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created logs directory: {log_dir}")
    
    def print_environment_status(self) -> None:
        """Print current environment status"""
        
        info = self.get_environment_info()
        validation = info['validation']
        
        print(f"🌍 Environment: {info['current_environment']}")
        print(f"📁 Active .env file: {info['active_env_file'] or 'None'}")
        
        if validation['valid']:
            print("✅ Environment validation: PASSED")
        else:
            print("❌ Environment validation: FAILED")
        
        if validation['errors']:
            print("\n❌ Errors:")
            for error in validation['errors']:
                print(f"   - {error}")
        
        if validation['warnings']:
            print("\n⚠️ Warnings:")
            for warning in validation['warnings']:
                print(f"   - {warning}")
        
        if validation['recommendations']:
            print("\n💡 Recommendations:")
            for rec in validation['recommendations']:
                print(f"   - {rec}")
