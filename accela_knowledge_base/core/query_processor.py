"""
Natural language query processor for Accela Knowledge Base
Processes natural language queries and extracts structured information
"""

import re
from typing import Dict, List, Optional, Tuple
from .logging import LoggerMixin


class NaturalLanguageQueryProcessor(LoggerMixin):
    """Processes natural language queries and extracts structured information"""
    
    def __init__(self):
        # County name mappings
        self.county_mappings = {
            'asheville': 'asheville',
            'santa barbara': 'santa_barbara',
            'santa_barbara': 'santa_barbara',
            'sb': 'santa_barbara',
            'marin': 'marin',
            'san mateo': 'san_mateo',
            'san_mateo': 'san_mateo',
            'alameda': 'alameda',
            'contra costa': 'contra_costa',
            'contra_costa': 'contra_costa',
            'sonoma': 'sonoma',
            'napa': 'napa',
            'solano': 'solano',
            'mendocino': 'mendocino'
        }
        
        # Use case patterns
        self.use_case_patterns = {
            r'email|notification|notify|alert': 'notification',
            r'permit|building|construction': 'permit_processing',
            r'inspection|inspect': 'inspection',
            r'license|licensing': 'licensing',
            r'fee|payment|cost|calculate': 'fee_calculation',
            r'workflow|automation|automatic': 'workflow_automation',
            r'document|generate|pdf|report': 'document_generation',
            r'condition|requirement': 'condition_management',
            r'address|parcel|gis': 'address_validation',
            r'renewal|expire|expiration': 'renewal_processing',
            r'compliance|zoning|code': 'compliance_checking',
            r'schedule|scheduling': 'scheduling'
        }
        
        # Comparison keywords
        self.comparison_keywords = [
            'compare', 'comparison', 'difference', 'differences', 'vs', 'versus',
            'between', 'contrast', 'similar', 'different', 'how does', 'what are the differences'
        ]
    
    def process_query(self, query: str) -> Dict[str, any]:
        """
        Process natural language query and extract structured information
        
        Args:
            query: Natural language query
            
        Returns:
            Dictionary with processed query information
        """
        
        self.logger.info(f"Processing natural language query: {query}")
        
        query_lower = query.lower()
        
        # Extract counties
        counties = self._extract_counties(query_lower)
        
        # Determine use case
        use_case = self._determine_use_case(query_lower)
        
        # Check if it's a comparison query
        is_comparison = self._is_comparison_query(query_lower)
        
        # Extract specific terms and context
        context = self._extract_context(query_lower)
        
        # Generate enhanced query for better matching
        enhanced_query = self._enhance_query(query, context, use_case)
        
        result = {
            'original_query': query,
            'enhanced_query': enhanced_query,
            'use_case': use_case,
            'target_counties': counties,
            'is_comparison': is_comparison,
            'context': context,
            'query_type': self._determine_query_type(query_lower, is_comparison)
        }
        
        self.logger.info(f"Processed query result: {result}")
        return result
    
    def _extract_counties(self, query: str) -> List[str]:
        """Extract county names from query"""
        
        counties = []
        
        for county_name, county_code in self.county_mappings.items():
            if county_name in query:
                if county_code not in counties:
                    counties.append(county_code)
        
        return counties
    
    def _determine_use_case(self, query: str) -> str:
        """Determine use case from query"""
        
        for pattern, use_case in self.use_case_patterns.items():
            if re.search(pattern, query):
                return use_case
        
        return 'general_query'
    
    def _is_comparison_query(self, query: str) -> bool:
        """Check if query is asking for comparison"""
        
        return any(keyword in query for keyword in self.comparison_keywords)
    
    def _extract_context(self, query: str) -> Dict[str, any]:
        """Extract additional context from query"""
        
        context = {
            'keywords': [],
            'accela_terms': [],
            'business_process': None,
            'technical_focus': None
        }
        
        # Extract Accela-specific terms
        accela_terms = [
            'asa', 'wtua', 'isa', 'pageflow', 'batch', 'interface',
            'aa.env', 'aa.cap', 'aa.people', 'aa.workflow', 'aa.inspection',
            'event script', 'workflow', 'condition', 'custom field'
        ]
        
        for term in accela_terms:
            if term in query:
                context['accela_terms'].append(term)
        
        # Extract business process indicators
        if any(word in query for word in ['approval', 'approve', 'issued', 'issue']):
            context['business_process'] = 'approval_process'
        elif any(word in query for word in ['submit', 'application', 'apply']):
            context['business_process'] = 'application_process'
        elif any(word in query for word in ['review', 'check', 'validate']):
            context['business_process'] = 'review_process'
        
        # Extract technical focus
        if any(word in query for word in ['script', 'code', 'function', 'implementation']):
            context['technical_focus'] = 'implementation'
        elif any(word in query for word in ['configuration', 'setup', 'settings']):
            context['technical_focus'] = 'configuration'
        
        return context
    
    def _enhance_query(self, original_query: str, context: Dict, use_case: str) -> str:
        """Enhance query with additional context for better matching"""
        
        enhanced_parts = [original_query]
        
        # Add use case specific terms
        if use_case == 'notification':
            enhanced_parts.append('email sendMail notification alert')
        elif use_case == 'permit_processing':
            enhanced_parts.append('permit building construction application')
        elif use_case == 'inspection':
            enhanced_parts.append('inspection schedule assign')
        elif use_case == 'workflow_automation':
            enhanced_parts.append('workflow automation WTUA')
        
        # Add Accela terms if found
        if context['accela_terms']:
            enhanced_parts.extend(context['accela_terms'])
        
        return ' '.join(enhanced_parts)
    
    def _determine_query_type(self, query: str, is_comparison: bool) -> str:
        """Determine the type of query"""
        
        if is_comparison:
            return 'comparison'
        elif any(word in query for word in ['how to', 'how do', 'how can']):
            return 'how_to'
        elif any(word in query for word in ['what is', 'what are', 'what does']):
            return 'what_is'
        elif any(word in query for word in ['show me', 'find', 'search', 'list']):
            return 'search'
        elif any(word in query for word in ['best', 'recommend', 'suggest']):
            return 'recommendation'
        else:
            return 'general'


class MarkdownFormatter(LoggerMixin):
    """Formats agentic query results into markdown"""
    
    def format_response(self, query_result: Dict, orchestration_result: any) -> str:
        """
        Format orchestration result into markdown
        
        Args:
            query_result: Processed query information
            orchestration_result: Result from multi-agent orchestration
            
        Returns:
            Formatted markdown string
        """
        
        self.logger.info("Formatting response to markdown")
        
        markdown_parts = []
        
        # Header
        markdown_parts.append(f"# 🎯 Accela Implementation Analysis")
        markdown_parts.append(f"**Query:** {query_result['original_query']}")
        markdown_parts.append(f"**Use Case:** {query_result['use_case'].replace('_', ' ').title()}")
        
        if query_result['target_counties']:
            counties_str = ', '.join(county.replace('_', ' ').title() for county in query_result['target_counties'])
            markdown_parts.append(f"**Target Counties:** {counties_str}")
        
        markdown_parts.append(f"**Confidence:** {orchestration_result.confidence:.1%}")
        markdown_parts.append("")
        
        # Best Implementation
        if orchestration_result.best_implementation:
            best = orchestration_result.best_implementation
            markdown_parts.append("## 🏆 Recommended Implementation")
            markdown_parts.append(f"**County:** {best.get('county', 'Unknown').replace('_', ' ').title()}")
            markdown_parts.append(f"**Relevance Score:** {best.get('score', 0):.3f}")
            
            metadata = best.get('metadata', {})
            if metadata.get('functions'):
                markdown_parts.append(f"**Key Functions:** {', '.join(metadata['functions'][:5])}")
            
            if metadata.get('event_prefix'):
                markdown_parts.append(f"**Event Pattern:** {metadata['event_prefix']}")
            
            if metadata.get('complexity'):
                markdown_parts.append(f"**Complexity:** {metadata['complexity'].title()}")
            
            if best.get('reasoning'):
                markdown_parts.append(f"**Why This Implementation:** {best['reasoning']}")
            
            markdown_parts.append("")
        
        # Comparison (if applicable)
        if query_result['is_comparison'] and len(orchestration_result.alternatives) > 0:
            markdown_parts.append("## 📊 Implementation Comparison")
            
            # Create comparison table
            markdown_parts.append("| County | Score | Complexity | Functions | Event Pattern |")
            markdown_parts.append("|--------|-------|------------|-----------|---------------|")
            
            # Add best implementation to comparison
            if orchestration_result.best_implementation:
                best = orchestration_result.best_implementation
                best_metadata = best.get('metadata', {})
                markdown_parts.append(f"| **{best.get('county', 'Unknown').replace('_', ' ').title()}** | {best.get('score', 0):.3f} | {best_metadata.get('complexity', 'Unknown').title()} | {len(best_metadata.get('functions', []))} | {best_metadata.get('event_prefix', 'N/A')} |")
            
            # Add alternatives
            for alt in orchestration_result.alternatives[:3]:
                alt_metadata = alt.get('metadata', {})
                markdown_parts.append(f"| {alt.get('county', 'Unknown').replace('_', ' ').title()} | {alt.get('score', 0):.3f} | {alt_metadata.get('complexity', 'Unknown').title()} | {len(alt_metadata.get('functions', []))} | {alt_metadata.get('event_prefix', 'N/A')} |")
            
            markdown_parts.append("")
        
        # Alternative Implementations
        elif orchestration_result.alternatives:
            markdown_parts.append("## 🔄 Alternative Implementations")
            
            for i, alt in enumerate(orchestration_result.alternatives[:3], 1):
                markdown_parts.append(f"### {i}. {alt.get('county', 'Unknown').replace('_', ' ').title()}")
                markdown_parts.append(f"- **Score:** {alt.get('score', 0):.3f}")
                
                alt_metadata = alt.get('metadata', {})
                if alt_metadata.get('functions'):
                    markdown_parts.append(f"- **Functions:** {', '.join(alt_metadata['functions'][:3])}")
                if alt_metadata.get('complexity'):
                    markdown_parts.append(f"- **Complexity:** {alt_metadata['complexity'].title()}")
                
                markdown_parts.append("")
        
        # Implementation Plan
        if orchestration_result.synthesis and orchestration_result.synthesis.get('implementation_plan'):
            markdown_parts.append("## 📋 Implementation Plan")
            
            for phase in orchestration_result.synthesis['implementation_plan']:
                markdown_parts.append(f"### Phase {phase['phase']}: {phase['title']}")
                markdown_parts.append(f"**Duration:** {phase['duration']}")
                markdown_parts.append("**Tasks:**")
                for task in phase['tasks']:
                    markdown_parts.append(f"- {task}")
                markdown_parts.append("")
        
        # Recommendations
        if orchestration_result.recommendations:
            markdown_parts.append("## 💡 Recommendations")
            for rec in orchestration_result.recommendations:
                markdown_parts.append(f"- {rec}")
            markdown_parts.append("")
        
        # Technical Details
        if orchestration_result.reasoning:
            markdown_parts.append("## 🔧 Technical Analysis")
            markdown_parts.append(orchestration_result.reasoning)
            markdown_parts.append("")
        
        # Processing Information
        markdown_parts.append("---")
        markdown_parts.append(f"*Analysis completed in {orchestration_result.processing_time:.2f} seconds*")
        
        return "\n".join(markdown_parts)
