"""
Natural language query processor for Accela Knowledge Base
Processes natural language queries and extracts structured information
"""

import re
from typing import Dict, List, Optional, Tuple
from .logging import LoggerMixin
from .code_analyzer import CodeAnalyzer


class NaturalLanguageQueryProcessor(LoggerMixin):
    """Processes natural language queries and extracts structured information"""
    
    def __init__(self):
        # County name mappings
        self.county_mappings = {
            'asheville': 'asheville',
            'santa barbara': 'santa_barbara',
            'santa_barbara': 'santa_barbara',
            'sb': 'santa_barbara',
            'marin': 'marin',
            'san mateo': 'san_mateo',
            'san_mateo': 'san_mateo',
            'alameda': 'alameda',
            'contra costa': 'contra_costa',
            'contra_costa': 'contra_costa',
            'sonoma': 'sonoma',
            'napa': 'napa',
            'solano': 'solano',
            'mendocino': 'mendocino'
        }
        
        # Use case patterns
        self.use_case_patterns = {
            r'email|notification|notify|alert': 'notification',
            r'permit|building|construction': 'permit_processing',
            r'inspection|inspect': 'inspection',
            r'license|licensing': 'licensing',
            r'fee|payment|cost|calculate': 'fee_calculation',
            r'workflow|automation|automatic': 'workflow_automation',
            r'document|generate|pdf|report': 'document_generation',
            r'condition|requirement': 'condition_management',
            r'address|parcel|gis': 'address_validation',
            r'renewal|expire|expiration': 'renewal_processing',
            r'compliance|zoning|code': 'compliance_checking',
            r'schedule|scheduling': 'scheduling'
        }
        
        # Comparison keywords
        self.comparison_keywords = [
            'compare', 'comparison', 'difference', 'differences', 'vs', 'versus',
            'between', 'contrast', 'similar', 'different', 'how does', 'what are the differences'
        ]
    
    def process_query(self, query: str) -> Dict[str, any]:
        """
        Process natural language query and extract structured information
        
        Args:
            query: Natural language query
            
        Returns:
            Dictionary with processed query information
        """
        
        self.logger.info(f"Processing natural language query: {query}")
        
        query_lower = query.lower()
        
        # Extract counties
        counties = self._extract_counties(query_lower)
        
        # Determine use case
        use_case = self._determine_use_case(query_lower)
        
        # Check if it's a comparison query
        is_comparison = self._is_comparison_query(query_lower)
        
        # Extract specific terms and context
        context = self._extract_context(query_lower)
        
        # Generate enhanced query for better matching
        enhanced_query = self._enhance_query(query, context, use_case)
        
        result = {
            'original_query': query,
            'enhanced_query': enhanced_query,
            'use_case': use_case,
            'target_counties': counties,
            'is_comparison': is_comparison,
            'context': context,
            'query_type': self._determine_query_type(query_lower, is_comparison)
        }
        
        self.logger.info(f"Processed query result: {result}")
        return result
    
    def _extract_counties(self, query: str) -> List[str]:
        """Extract county names from query"""
        
        counties = []
        
        for county_name, county_code in self.county_mappings.items():
            if county_name in query:
                if county_code not in counties:
                    counties.append(county_code)
        
        return counties
    
    def _determine_use_case(self, query: str) -> str:
        """Determine use case from query"""
        
        for pattern, use_case in self.use_case_patterns.items():
            if re.search(pattern, query):
                return use_case
        
        return 'general_query'
    
    def _is_comparison_query(self, query: str) -> bool:
        """Check if query is asking for comparison"""
        
        return any(keyword in query for keyword in self.comparison_keywords)
    
    def _extract_context(self, query: str) -> Dict[str, any]:
        """Extract additional context from query"""
        
        context = {
            'keywords': [],
            'accela_terms': [],
            'business_process': None,
            'technical_focus': None
        }
        
        # Extract Accela-specific terms
        accela_terms = [
            'asa', 'wtua', 'isa', 'pageflow', 'batch', 'interface',
            'aa.env', 'aa.cap', 'aa.people', 'aa.workflow', 'aa.inspection',
            'event script', 'workflow', 'condition', 'custom field'
        ]
        
        for term in accela_terms:
            if term in query:
                context['accela_terms'].append(term)
        
        # Extract business process indicators
        if any(word in query for word in ['approval', 'approve', 'issued', 'issue']):
            context['business_process'] = 'approval_process'
        elif any(word in query for word in ['submit', 'application', 'apply']):
            context['business_process'] = 'application_process'
        elif any(word in query for word in ['review', 'check', 'validate']):
            context['business_process'] = 'review_process'
        
        # Extract technical focus
        if any(word in query for word in ['script', 'code', 'function', 'implementation']):
            context['technical_focus'] = 'implementation'
        elif any(word in query for word in ['configuration', 'setup', 'settings']):
            context['technical_focus'] = 'configuration'
        
        return context
    
    def _enhance_query(self, original_query: str, context: Dict, use_case: str) -> str:
        """Enhance query with additional context for better matching"""
        
        enhanced_parts = [original_query]
        
        # Add use case specific terms
        if use_case == 'notification':
            enhanced_parts.append('email sendMail notification alert')
        elif use_case == 'permit_processing':
            enhanced_parts.append('permit building construction application')
        elif use_case == 'inspection':
            enhanced_parts.append('inspection schedule assign')
        elif use_case == 'workflow_automation':
            enhanced_parts.append('workflow automation WTUA')
        
        # Add Accela terms if found
        if context['accela_terms']:
            enhanced_parts.extend(context['accela_terms'])
        
        return ' '.join(enhanced_parts)
    
    def _determine_query_type(self, query: str, is_comparison: bool) -> str:
        """Determine the type of query"""
        
        if is_comparison:
            return 'comparison'
        elif any(word in query for word in ['how to', 'how do', 'how can']):
            return 'how_to'
        elif any(word in query for word in ['what is', 'what are', 'what does']):
            return 'what_is'
        elif any(word in query for word in ['show me', 'find', 'search', 'list']):
            return 'search'
        elif any(word in query for word in ['best', 'recommend', 'suggest']):
            return 'recommendation'
        else:
            return 'general'


class MarkdownFormatter(LoggerMixin):
    """Formats agentic query results into markdown with detailed code analysis"""

    def __init__(self):
        self.code_analyzer = CodeAnalyzer()

    def format_response(self, query_result: Dict, orchestration_result: any) -> str:
        """
        Format orchestration result into markdown
        
        Args:
            query_result: Processed query information
            orchestration_result: Result from multi-agent orchestration
            
        Returns:
            Formatted markdown string
        """
        
        self.logger.info("Formatting response to markdown")

        # Debug logging
        self.logger.info(f"Orchestration result type: {type(orchestration_result)}")
        self.logger.info(f"Best implementation type: {type(orchestration_result.best_implementation)}")
        self.logger.info(f"Alternatives type: {type(orchestration_result.alternatives)}")
        if orchestration_result.alternatives:
            self.logger.info(f"First alternative type: {type(orchestration_result.alternatives[0]) if orchestration_result.alternatives else 'None'}")
        
        markdown_parts = []
        
        # Header
        markdown_parts.append(f"# 🎯 Accela Implementation Analysis")
        markdown_parts.append(f"**Query:** {query_result['original_query']}")
        markdown_parts.append(f"**Use Case:** {query_result['use_case'].replace('_', ' ').title()}")
        
        if query_result['target_counties']:
            counties_str = ', '.join(county.replace('_', ' ').title() for county in query_result['target_counties'])
            markdown_parts.append(f"**Target Counties:** {counties_str}")
        
        markdown_parts.append(f"**Confidence:** {orchestration_result.confidence:.1%}")
        markdown_parts.append("")
        
        # Best Implementation with detailed code analysis
        if orchestration_result.best_implementation:
            best = orchestration_result.best_implementation
            county = best.get('county', 'Unknown')

            markdown_parts.append("## 🏆 Recommended Implementation")
            markdown_parts.append(f"**County:** {county.replace('_', ' ').title()}")
            markdown_parts.append(f"**Relevance Score:** {best.get('score', 0):.3f}")

            metadata = best.get('metadata', {})
            if metadata.get('functions'):
                markdown_parts.append(f"**Key Functions:** {', '.join(metadata['functions'][:5])}")

            if metadata.get('event_prefix'):
                markdown_parts.append(f"**Event Pattern:** {metadata['event_prefix']}")

            if metadata.get('complexity'):
                markdown_parts.append(f"**Complexity:** {metadata['complexity'].title()}")

            if best.get('reasoning'):
                markdown_parts.append(f"**Why This Implementation:** {best['reasoning']}")

            # Add detailed code analysis
            try:
                code_analysis = self.code_analyzer.get_implementation_code(county, query_result['use_case'])
                if code_analysis:
                    markdown_parts.append(self._format_code_analysis(code_analysis))
            except Exception as e:
                self.logger.error(f"Code analysis failed for {county}: {e}")
                markdown_parts.append("*Code analysis temporarily unavailable*")

            markdown_parts.append("")
        
        # Enhanced Comparison (if applicable)
        if query_result['is_comparison'] and orchestration_result.alternatives and len(orchestration_result.alternatives) > 0:
            markdown_parts.append("## 📊 Detailed Implementation Comparison")

            # Get the top two implementations for detailed comparison
            best = orchestration_result.best_implementation
            alt = orchestration_result.alternatives[0] if orchestration_result.alternatives else None

            if best and alt and isinstance(best, dict) and isinstance(alt, dict):
                county1 = best.get('county', 'Unknown')
                county2 = alt.get('county', 'Unknown')

                # Perform detailed code comparison
                try:
                    comparison = self.code_analyzer.compare_implementations(
                        county1, county2, query_result['use_case']
                    )
                    markdown_parts.append(self._format_detailed_comparison(comparison))
                except Exception as e:
                    self.logger.error(f"Code comparison failed: {e}")
                    # Fallback to basic comparison
                    markdown_parts.append(self._format_basic_comparison(orchestration_result))
            else:
                # Fallback to basic comparison table
                markdown_parts.append(self._format_basic_comparison(orchestration_result))

            markdown_parts.append("")
        
        # Alternative Implementations
        elif orchestration_result.alternatives and len(orchestration_result.alternatives) > 0:
            markdown_parts.append("## 🔄 Alternative Implementations")

            for i, alt in enumerate(orchestration_result.alternatives[:3], 1):
                if isinstance(alt, dict):
                    markdown_parts.append(f"### {i}. {alt.get('county', 'Unknown').replace('_', ' ').title()}")
                    markdown_parts.append(f"- **Score:** {alt.get('score', 0):.3f}")

                    alt_metadata = alt.get('metadata', {})
                    if isinstance(alt_metadata, dict):
                        if alt_metadata.get('functions'):
                            markdown_parts.append(f"- **Functions:** {', '.join(alt_metadata['functions'][:3])}")
                        if alt_metadata.get('complexity'):
                            markdown_parts.append(f"- **Complexity:** {alt_metadata['complexity'].title()}")

                    markdown_parts.append("")
        
        # Implementation Plan
        if orchestration_result.synthesis and orchestration_result.synthesis.get('implementation_plan'):
            markdown_parts.append("## 📋 Implementation Plan")
            
            for phase in orchestration_result.synthesis['implementation_plan']:
                markdown_parts.append(f"### Phase {phase['phase']}: {phase['title']}")
                markdown_parts.append(f"**Duration:** {phase['duration']}")
                markdown_parts.append("**Tasks:**")
                for task in phase['tasks']:
                    markdown_parts.append(f"- {task}")
                markdown_parts.append("")
        
        # Recommendations
        if orchestration_result.recommendations:
            markdown_parts.append("## 💡 Recommendations")
            for rec in orchestration_result.recommendations:
                markdown_parts.append(f"- {rec}")
            markdown_parts.append("")
        
        # Technical Details
        if orchestration_result.reasoning:
            markdown_parts.append("## 🔧 Technical Analysis")
            markdown_parts.append(orchestration_result.reasoning)
            markdown_parts.append("")
        
        # Processing Information
        markdown_parts.append("---")
        markdown_parts.append(f"*Analysis completed in {orchestration_result.processing_time:.2f} seconds*")
        
        return "\n".join(markdown_parts)

    def _format_basic_comparison(self, orchestration_result) -> str:
        """Format basic comparison table"""

        parts = []
        parts.append("| County | Score | Complexity | Functions | Event Pattern |")
        parts.append("|--------|-------|------------|-----------|---------------|")

        # Add best implementation to comparison
        if orchestration_result.best_implementation and isinstance(orchestration_result.best_implementation, dict):
            best = orchestration_result.best_implementation
            best_metadata = best.get('metadata', {}) if isinstance(best.get('metadata'), dict) else {}
            functions_count = len(best_metadata.get('functions', [])) if isinstance(best_metadata.get('functions'), list) else 0
            parts.append(f"| **{best.get('county', 'Unknown').replace('_', ' ').title()}** | {best.get('score', 0):.3f} | {best_metadata.get('complexity', 'Unknown').title()} | {functions_count} | {best_metadata.get('event_prefix', 'N/A')} |")

        # Add alternatives
        if orchestration_result.alternatives:
            for alt in orchestration_result.alternatives[:3]:
                if isinstance(alt, dict):
                    alt_metadata = alt.get('metadata', {}) if isinstance(alt.get('metadata'), dict) else {}
                    functions_count = len(alt_metadata.get('functions', [])) if isinstance(alt_metadata.get('functions'), list) else 0
                    parts.append(f"| {alt.get('county', 'Unknown').replace('_', ' ').title()} | {alt.get('score', 0):.3f} | {alt_metadata.get('complexity', 'Unknown').title()} | {functions_count} | {alt_metadata.get('event_prefix', 'N/A')} |")

        return "\n".join(parts)

    def _format_code_analysis(self, code_analysis: Dict) -> str:
        """Format detailed code analysis"""

        parts = []

        # Email Functions
        if code_analysis.get('email_functions'):
            parts.append("### 📧 Email Implementation Details")

            for func in code_analysis['email_functions'][:3]:  # Show top 3
                parts.append(f"#### `{func['name']}()`")

                if func.get('key_features'):
                    parts.append("**Features:**")
                    for feature in func['key_features']:
                        parts.append(f"- {feature}")

                if func.get('code'):
                    # Show key parts of the code
                    code_preview = self._extract_code_preview(func['code'])
                    if code_preview:
                        parts.append("**Key Code:**")
                        parts.append("```javascript")
                        parts.append(code_preview)
                        parts.append("```")

                parts.append("")

        # Workflow Functions
        if code_analysis.get('workflow_functions'):
            parts.append("### ⚙️ Workflow Implementation")

            for func in code_analysis['workflow_functions'][:2]:
                parts.append(f"#### `{func['name']}()`")

                if func.get('key_features'):
                    for feature in func['key_features']:
                        parts.append(f"- {feature}")

                parts.append("")

        # Key Code Snippets
        if code_analysis.get('code_snippets'):
            parts.append("### 🔧 Key Code Snippets")

            snippet_types = {}
            for snippet in code_analysis['code_snippets']:
                snippet_type = snippet.get('type', 'General')
                if snippet_type not in snippet_types:
                    snippet_types[snippet_type] = []
                snippet_types[snippet_type].append(snippet)

            for snippet_type, snippets in snippet_types.items():
                parts.append(f"#### {snippet_type}")
                parts.append("```javascript")
                for snippet in snippets[:3]:  # Show top 3 per type
                    parts.append(snippet.get('snippet', ''))
                parts.append("```")
                parts.append("")

        # Patterns
        if code_analysis.get('patterns'):
            parts.append("### 📋 Code Patterns")

            for pattern in code_analysis['patterns']:
                parts.append(f"- **{pattern['type']}**: {pattern.get('count', 0)} instances")
                if pattern.get('examples'):
                    parts.append(f"  - Examples: {', '.join(pattern['examples'][:3])}")

            parts.append("")

        return "\n".join(parts)

    def _extract_code_preview(self, code: str) -> str:
        """Extract a meaningful preview of the code"""

        lines = code.split('\n')
        preview_lines = []

        # Look for key lines
        for line in lines:
            line = line.strip()
            if not line or line.startswith('//'):
                continue

            # Include important lines
            if any(keyword in line.lower() for keyword in
                   ['sendmail', 'aa.', 'email', 'notify', 'workflow', 'if', 'function']):
                preview_lines.append(line)

                if len(preview_lines) >= 10:  # Limit preview size
                    break

        return '\n'.join(preview_lines) if preview_lines else code[:500]

    def _format_detailed_comparison(self, comparison: Dict) -> str:
        """Format detailed comparison between two implementations"""

        parts = []
        county1 = comparison['county1'].replace('_', ' ').title()
        county2 = comparison['county2'].replace('_', ' ').title()

        parts.append(f"### {county1} vs {county2}")
        parts.append("")

        # Email Approaches
        email_diff = comparison['differences']['email_approaches']
        parts.append("#### 📧 Email Implementation Differences")
        parts.append(f"- **{county1}**: {email_diff['county1_functions']} email functions")
        parts.append(f"- **{county2}**: {email_diff['county2_functions']} email functions")

        if email_diff.get('county1_features'):
            parts.append(f"- **{county1} Features**: {', '.join(email_diff['county1_features'][0] if email_diff['county1_features'] else [])}")

        if email_diff.get('county2_features'):
            parts.append(f"- **{county2} Features**: {', '.join(email_diff['county2_features'][0] if email_diff['county2_features'] else [])}")

        parts.append("")

        # Complexity Comparison
        complexity = comparison['differences']['code_complexity']
        parts.append("#### 🔧 Complexity Analysis")
        parts.append(f"- **{county1}**: {complexity['county1_complexity']} code elements")
        parts.append(f"- **{county2}**: {complexity['county2_complexity']} code elements")
        parts.append(f"- **More Complex**: {complexity['more_complex'].replace('_', ' ').title()}")
        parts.append("")

        # Unique Features
        unique = comparison['differences']['unique_features']
        if unique['county1_unique']:
            parts.append(f"#### 🌟 {county1} Unique Features")
            for feature in unique['county1_unique']:
                parts.append(f"- {feature}")
            parts.append("")

        if unique['county2_unique']:
            parts.append(f"#### 🌟 {county2} Unique Features")
            for feature in unique['county2_unique']:
                parts.append(f"- {feature}")
            parts.append("")

        # Common Features
        if unique['common_features']:
            parts.append("#### 🤝 Common Features")
            for feature in unique['common_features']:
                parts.append(f"- {feature}")
            parts.append("")

        # Similarities
        if comparison.get('similarities'):
            parts.append("#### 🔄 Similarities")
            for similarity in comparison['similarities']:
                parts.append(f"- {similarity}")
            parts.append("")

        # Recommendations
        if comparison.get('recommendations'):
            parts.append("#### 💡 Comparison Recommendations")
            for rec in comparison['recommendations']:
                parts.append(f"- {rec}")
            parts.append("")

        return "\n".join(parts)
