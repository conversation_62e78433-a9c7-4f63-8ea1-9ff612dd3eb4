"""
Main CLI entry point for Accela Knowledge Base
"""

import click
from ..core.config import Config
from ..core.logging import setup_logging


@click.group()
@click.option('--config-file', help='Configuration file path')
@click.option('--log-level', default='INFO', help='Logging level')
@click.option('--log-file', help='Log file path')
@click.pass_context
def main(ctx, config_file, log_level, log_file):
    """Accela Knowledge Base CLI"""
    
    # Setup logging
    setup_logging(level=log_level, log_file=log_file)
    
    # Load configuration
    if config_file:
        # TODO: Implement config file loading
        config = Config.from_env()
    else:
        config = Config.from_env()
    
    # Store in context
    ctx.ensure_object(dict)
    ctx.obj['config'] = config


@main.command("extract-metadata")
@click.pass_context
def extract_metadata(ctx):
    """Extract metadata from Accela scripts"""
    config = ctx.obj['config']
    
    from ..data.metadata_extractor import MetadataExtractor
    
    extractor = MetadataExtractor(config)
    scripts = extractor.extract_all()
    
    click.echo(f"✅ Extracted metadata from {len(scripts)} scripts")


@main.command("build-graph")
@click.pass_context
def build_graph(ctx):
    """Build knowledge graph from metadata"""
    config = ctx.obj['config']
    
    from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
    
    graph = AccelaKnowledgeGraph(config)
    graph.build_from_metadata()
    
    stats = graph.get_stats()
    click.echo(f"✅ Graph built: {stats['nodes']} nodes, {stats['edges']} edges")


@main.command()
@click.option('--host', default=None, help='API host')
@click.option('--port', default=None, type=int, help='API port')
@click.option('--workers', default=None, type=int, help='Number of workers')
@click.pass_context
def serve(ctx, host, port, workers):
    """Start the API server"""
    config = ctx.obj['config']
    
    # Override config with CLI options
    if host:
        config.api_host = host
    if port:
        config.api_port = port
    if workers:
        config.api_workers = workers
    
    from ..api.app import create_app
    import uvicorn
    
    app = create_app(config)
    
    click.echo(f"🚀 Starting API server on {config.api_host}:{config.api_port}")
    
    uvicorn.run(
        app,
        host=config.api_host,
        port=config.api_port,
        workers=config.api_workers if config.api_workers > 1 else None
    )


@main.command()
@click.option('--query', required=True, help='Query to process')
@click.option('--use-case', required=True, help='Use case for the query')
@click.option('--counties', help='Comma-separated list of target counties')
@click.pass_context
def query(ctx, query, use_case, counties):
    """Process a query using the agentic system"""
    config = ctx.obj['config']
    
    from ..knowledge_graph.graph_builder import AccelaKnowledgeGraph
    from ..agents.orchestrator import MultiAgentOrchestrator
    from ..core.models import OrchestrationRequest
    import asyncio
    import uuid
    
    # Initialize system
    graph = AccelaKnowledgeGraph(config)
    graph.build_from_metadata()
    
    orchestrator = MultiAgentOrchestrator(graph, config)
    
    # Create request
    target_counties = counties.split(',') if counties else None
    request = OrchestrationRequest(
        request_id=str(uuid.uuid4()),
        query=query,
        use_case=use_case,
        target_counties=target_counties
    )
    
    # Process query
    async def process():
        result = await orchestrator.orchestrate(request)
        
        click.echo(f"\n🎯 Query: {result.query}")
        click.echo(f"📊 Confidence: {result.confidence:.2f}")
        
        if result.best_implementation:
            best = result.best_implementation
            click.echo(f"\n🏆 Best Implementation:")
            click.echo(f"   County: {best.get('county', 'Unknown')}")
            click.echo(f"   Score: {best.get('score', 0):.3f}")
        
        click.echo(f"\n💡 Recommendations:")
        for i, rec in enumerate(result.recommendations[:3], 1):
            click.echo(f"   {i}. {rec}")
    
    asyncio.run(process())


@main.command()
@click.pass_context
def test(ctx):
    """Run system tests"""
    config = ctx.obj['config']

    from ..tests.test_runner import TestRunner

    runner = TestRunner(config)
    results = runner.run_all()

    if results['success']:
        click.echo("✅ All tests passed")
    else:
        click.echo("❌ Some tests failed")
        for failure in results['failures']:
            click.echo(f"   - {failure}")


@main.group()
def env():
    """Environment management commands"""
    pass


@env.command("status")
def env_status():
    """Show current environment status"""
    from ..core.env_manager import EnvironmentManager

    env_manager = EnvironmentManager()
    env_manager.print_environment_status()


@env.command("init")
def env_init():
    """Initialize .env file from example"""
    from pathlib import Path
    import shutil

    if Path('.env').exists():
        click.echo("✅ .env file already exists")
        return

    if Path('.env.example').exists():
        shutil.copy2('.env.example', '.env')
        click.echo("✅ Created .env file from .env.example")
        click.echo("💡 Edit .env file to configure your settings")
    else:
        click.echo("❌ .env.example file not found")


@env.command("validate")
def env_validate():
    """Validate current environment configuration"""
    from ..core.env_manager import EnvironmentManager

    env_manager = EnvironmentManager()
    validation = env_manager.validate_environment()

    if validation['valid']:
        click.echo("✅ Environment validation passed")
    else:
        click.echo("❌ Environment validation failed")

    if validation['errors']:
        click.echo("\nErrors:")
        for error in validation['errors']:
            click.echo(f"   - {error}")

    if validation['warnings']:
        click.echo("\nWarnings:")
        for warning in validation['warnings']:
            click.echo(f"   - {warning}")

    if validation['recommendations']:
        click.echo("\nRecommendations:")
        for rec in validation['recommendations']:
            click.echo(f"   - {rec}")


@env.command("info")
def env_info():
    """Show detailed environment information"""
    from ..core.env_manager import EnvironmentManager
    import json

    env_manager = EnvironmentManager()
    info = env_manager.get_environment_info()

    click.echo(json.dumps(info, indent=2))


if __name__ == '__main__':
    main()
