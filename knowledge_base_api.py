#!/usr/bin/env python3
"""
FastAPI-based Knowledge Base API for Accela implementations
Provides REST endpoints for querying the knowledge base
"""

from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import os
from vector_db_builder import AccelaVectorDB

# Pydantic models for API
class SearchRequest(BaseModel):
    query: str
    k: int = 10
    filters: Optional[Dict[str, Any]] = None

class SearchResult(BaseModel):
    content: str
    metadata: Dict[str, Any]
    score: float
    id: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total_results: int
    query: str

class CompareRequest(BaseModel):
    counties: List[str]
    script_type: Optional[str] = None
    module: Optional[str] = None
    search_term: Optional[str] = None

# Initialize FastAPI app
app = FastAPI(
    title="Accela Knowledge Base API",
    description="API for querying Accela implementations across multiple counties",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global vector database instance
vector_db = None

@app.on_event("startup")
async def startup_event():
    """Initialize vector database on startup"""
    global vector_db
    
    if not os.path.exists("vector_index"):
        raise RuntimeError("Vector index not found. Please run vector_db_builder.py first.")
    
    vector_db = AccelaVectorDB()
    vector_db.load_index()
    print("Vector database loaded successfully")

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Accela Knowledge Base API",
        "version": "1.0.0",
        "endpoints": {
            "/search/semantic": "Semantic search across all scripts",
            "/search/code": "Search for specific functions or code patterns",
            "/compare/counties": "Compare implementations across counties",
            "/patterns/common": "Find common patterns and functions",
            "/stats": "Get database statistics"
        }
    }

@app.post("/search/semantic", response_model=SearchResponse)
async def semantic_search(request: SearchRequest):
    """Perform semantic search across all Accela scripts"""
    if not vector_db:
        raise HTTPException(status_code=500, detail="Vector database not initialized")
    
    try:
        results = vector_db.search(
            query=request.query,
            k=request.k,
            filters=request.filters
        )
        
        search_results = [
            SearchResult(
                content=result['content'],
                metadata=result['metadata'],
                score=result['score'],
                id=result['id']
            )
            for result in results
        ]
        
        return SearchResponse(
            results=search_results,
            total_results=len(search_results),
            query=request.query
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.get("/search/code")
async def code_search(
    function: str = Query(..., description="Function name to search for"),
    county: Optional[str] = Query(None, description="Filter by county"),
    script_type: Optional[str] = Query(None, description="Filter by script type"),
    k: int = Query(10, description="Number of results to return")
):
    """Search for specific functions or code patterns"""
    if not vector_db:
        raise HTTPException(status_code=500, detail="Vector database not initialized")
    
    # Build search query
    query = f"function {function}"
    
    # Build filters
    filters = {}
    if county:
        filters['county'] = county
    if script_type:
        filters['script_type'] = script_type
    
    try:
        results = vector_db.search(query=query, k=k, filters=filters if filters else None)
        
        # Filter results that actually contain the function
        filtered_results = []
        for result in results:
            if function.lower() in result['content'].lower():
                filtered_results.append(result)
        
        return {
            "function": function,
            "results": filtered_results,
            "total_results": len(filtered_results)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Code search failed: {str(e)}")

@app.post("/compare/counties")
async def compare_counties(request: CompareRequest):
    """Compare implementations across different counties"""
    if not vector_db:
        raise HTTPException(status_code=500, detail="Vector database not initialized")
    
    try:
        comparison_results = {}
        
        for county in request.counties:
            filters = {'county': county}
            if request.script_type:
                filters['script_type'] = request.script_type
            if request.module:
                filters['module'] = request.module
            
            search_query = request.search_term or f"{request.script_type or ''} {request.module or ''}".strip()
            if not search_query:
                search_query = "script implementation"
            
            results = vector_db.search(
                query=search_query,
                k=5,
                filters=filters
            )
            
            comparison_results[county] = {
                'results': results,
                'total_scripts': len([doc for doc in vector_db.documents 
                                    if doc.metadata.get('county') == county]),
                'matching_results': len(results)
            }
        
        return {
            "comparison": comparison_results,
            "counties": request.counties,
            "filters": {
                "script_type": request.script_type,
                "module": request.module,
                "search_term": request.search_term
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")

@app.get("/patterns/common")
async def find_common_patterns(
    module: Optional[str] = Query(None, description="Filter by module"),
    script_type: Optional[str] = Query(None, description="Filter by script type"),
    min_counties: int = Query(2, description="Minimum number of counties that must have the pattern")
):
    """Find common patterns and functions across counties"""
    if not vector_db:
        raise HTTPException(status_code=500, detail="Vector database not initialized")
    
    try:
        # Analyze function usage across counties
        function_usage = {}
        county_scripts = {}
        
        for doc in vector_db.documents:
            metadata = doc.metadata
            county = metadata.get('county')
            functions = metadata.get('functions', [])
            
            # Apply filters
            if module and metadata.get('module') != module:
                continue
            if script_type and metadata.get('script_type') != script_type:
                continue
            
            if county not in county_scripts:
                county_scripts[county] = set()
            
            for func in functions:
                if func not in function_usage:
                    function_usage[func] = set()
                function_usage[func].add(county)
                county_scripts[county].add(func)
        
        # Find functions used by multiple counties
        common_functions = {}
        for func, counties in function_usage.items():
            if len(counties) >= min_counties:
                common_functions[func] = {
                    'counties': list(counties),
                    'county_count': len(counties)
                }
        
        # Sort by usage frequency
        sorted_functions = dict(sorted(
            common_functions.items(),
            key=lambda x: x[1]['county_count'],
            reverse=True
        ))
        
        return {
            "common_functions": sorted_functions,
            "total_counties": len(county_scripts),
            "filters": {
                "module": module,
                "script_type": script_type,
                "min_counties": min_counties
            },
            "county_function_counts": {
                county: len(functions) for county, functions in county_scripts.items()
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Pattern analysis failed: {str(e)}")

@app.get("/stats")
async def get_statistics():
    """Get database statistics"""
    if not vector_db:
        raise HTTPException(status_code=500, detail="Vector database not initialized")
    
    try:
        # Count documents by various categories
        county_counts = {}
        script_type_counts = {}
        module_counts = {}
        complexity_counts = {}
        
        for doc in vector_db.documents:
            metadata = doc.metadata
            
            # County counts
            county = metadata.get('county', 'unknown')
            county_counts[county] = county_counts.get(county, 0) + 1
            
            # Script type counts
            script_type = metadata.get('script_type', 'unknown')
            script_type_counts[script_type] = script_type_counts.get(script_type, 0) + 1
            
            # Module counts
            module = metadata.get('module', 'unknown')
            module_counts[module] = module_counts.get(module, 0) + 1
            
            # Complexity counts
            complexity = metadata.get('complexity', 'unknown')
            complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1
        
        return {
            "total_documents": len(vector_db.documents),
            "total_counties": len(county_counts),
            "breakdown": {
                "by_county": county_counts,
                "by_script_type": script_type_counts,
                "by_module": module_counts,
                "by_complexity": complexity_counts
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Statistics generation failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "knowledge_base_api:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
