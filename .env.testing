# Testing Environment Configuration

# Core configuration - Test data
ACCELA_SRC_DIRECTORY=tests/data/src
ACCELA_METADATA_FILE=tests/data/test_metadata.json
ACCELA_GRAPH_EXPORT_FILE=tests/data/test_graph.gexf

# API configuration - Testing
ACCELA_API_HOST=127.0.0.1
ACCELA_API_PORT=8002
ACCELA_API_WORKERS=1
ACCELA_API_SECRET_KEY=test-secret-key
ACCELA_API_CORS_ORIGINS=*

# LLM configuration - Disabled for testing
# OPENAI_API_KEY=
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=100
ACCELA_LLM_MAX_TOKENS_REASONING=50
ACCELA_LLM_TEMPERATURE=0.0

# Agent configuration - Fast for testing
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=1
ACCELA_SIMILARITY_THRESHOLD=0.1
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.2

# Performance settings - Minimal for testing
ACCELA_MAX_CODE_LENGTH_FOR_LLM=500
ACCELA_MAX_SEARCH_RESULTS=5
ACCELA_CACHE_ENABLED=false

# Logging - Minimal for testing
ACCELA_LOG_LEVEL=WARNING
ACCELA_LOG_FILE=tests/logs/test.log

# Testing settings
ACCELA_DEBUG=false
ACCELA_RELOAD=false
ACCELA_TEST_MODE=true
