# Environment Configuration Guide

## 🌍 **Complete Environment Management System**

The Accela Knowledge Base now includes a comprehensive environment configuration system with proper `.env` file support, environment-specific settings, and validation.

## 📁 **Environment Files Structure**

```
.env.example        # Template with all available options
.env               # Active configuration (auto-created)
.env.development   # Development-specific settings
.env.production    # Production-specific settings  
.env.testing       # Testing-specific settings
.gitignore         # Properly excludes sensitive files
```

## 🚀 **Quick Setup**

### **1. Automatic Setup (Recommended)**
```bash
# Run the production setup script
chmod +x setup_production.sh
./setup_production.sh

# This automatically:
# - Creates .env from .env.example
# - Sets up development environment
# - Validates configuration
# - Creates logs directory
```

### **2. Manual Setup**
```bash
# Copy example to active config
cp .env.example .env

# Edit your settings
nano .env

# Setup specific environment
accela-kb env setup development  # or production, testing

# Validate configuration
accela-kb env validate
```

## ⚙️ **Configuration Categories**

### **Core Configuration**
```bash
ACCELA_SRC_DIRECTORY=src                    # Source code directory
ACCELA_METADATA_FILE=accela_metadata.json  # Metadata storage
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf  # Graph export
```

### **API Configuration**
```bash
ACCELA_API_HOST=0.0.0.0                    # API host
ACCELA_API_PORT=8001                       # API port
ACCELA_API_WORKERS=4                       # Worker processes
ACCELA_API_SECRET_KEY=your-secret-key      # Security key
ACCELA_API_CORS_ORIGINS=*                  # CORS origins
```

### **LLM Configuration (Optional)**
```bash
OPENAI_API_KEY=your-api-key-here           # OpenAI API key
ACCELA_LLM_MODEL=gpt-3.5-turbo            # LLM model
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300        # Analysis token limit
ACCELA_LLM_MAX_TOKENS_REASONING=150       # Reasoning token limit
ACCELA_LLM_TEMPERATURE=0.1                # LLM temperature
```

### **Agent Configuration**
```bash
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=2      # Relevance threshold
ACCELA_SIMILARITY_THRESHOLD=0.3            # Similarity threshold
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.4   # Function similarity
```

### **Performance Settings**
```bash
ACCELA_MAX_CODE_LENGTH_FOR_LLM=2000       # Max code length for LLM
ACCELA_MAX_SEARCH_RESULTS=20              # Max search results
ACCELA_CACHE_ENABLED=true                 # Enable caching
```

### **Logging Configuration**
```bash
ACCELA_LOG_LEVEL=INFO                     # Log level
ACCELA_LOG_FILE=logs/accela_kb.log        # Log file path
```

## 🎯 **Environment-Specific Configurations**

### **Development Environment**
```bash
# Setup development environment
accela-kb env setup development

# Features:
# - Single worker for easier debugging
# - Debug logging enabled
# - Cache disabled for fresh data
# - Relaxed thresholds for testing
# - Auto-reload enabled
```

### **Production Environment**
```bash
# Setup production environment
accela-kb env setup production

# Features:
# - Multiple workers for performance
# - INFO level logging
# - Cache enabled for performance
# - Optimized thresholds
# - Security validations
# - CORS restrictions
```

### **Testing Environment**
```bash
# Setup testing environment
accela-kb env setup testing

# Features:
# - Minimal resources
# - Test data paths
# - LLM disabled by default
# - Fast execution settings
# - Separate test logs
```

## 🔧 **Environment Management CLI**

### **Status and Information**
```bash
# Show current environment status
accela-kb env status

# Show detailed environment information
accela-kb env info

# Validate current configuration
accela-kb env validate
```

### **Environment Setup**
```bash
# Setup specific environment
accela-kb env setup development
accela-kb env setup production
accela-kb env setup testing
```

## ✅ **Environment Validation**

The system includes comprehensive validation:

### **Automatic Checks**
- ✅ Required directories exist
- ✅ API port is valid (1-65535)
- ✅ Worker count is reasonable
- ✅ Secret key strength (production)
- ✅ CORS configuration security
- ✅ Log file permissions

### **Environment-Specific Validation**
- **Production**: Strong secret keys, secure CORS, appropriate logging
- **Development**: Reasonable resource usage, debug settings
- **Testing**: Test mode enabled, minimal resources

### **Validation Commands**
```bash
# Validate current environment
accela-kb env validate

# Example output:
✅ Environment validation: PASSED
💡 Recommendations:
   - Set OPENAI_API_KEY for enhanced LLM intelligence (optional)
   - Consider restricting CORS origins in production
```

## 🔒 **Security Best Practices**

### **Production Security**
```bash
# Strong secret key (32+ characters)
ACCELA_API_SECRET_KEY=your-very-strong-secret-key-here-32-chars-minimum

# Restrict CORS origins
ACCELA_API_CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Disable debug mode
ACCELA_DEBUG=false

# Use INFO or WARNING log level
ACCELA_LOG_LEVEL=INFO
```

### **API Key Management**
```bash
# Development
OPENAI_API_KEY=sk-dev-key-here

# Production (use environment variables or secrets management)
export OPENAI_API_KEY="sk-prod-key-here"
```

## 📊 **Environment Detection**

The system automatically detects environment based on:

1. **Explicit setting**: `ACCELA_ENVIRONMENT=production`
2. **Test mode**: `ACCELA_TEST_MODE=true` → testing
3. **Debug mode**: `ACCELA_DEBUG=true` → development  
4. **Worker count**: `ACCELA_API_WORKERS>1` → production
5. **Default**: development

## 🚀 **Deployment Examples**

### **Development**
```bash
# Local development
accela-kb env setup development
accela-kb serve --host 127.0.0.1 --port 8001 --workers 1
```

### **Production with Docker**
```dockerfile
FROM python:3.11-slim
COPY . /app
WORKDIR /app
RUN pip install -e .

# Copy production environment
COPY .env.production .env

EXPOSE 8001
CMD ["accela-kb", "serve", "--host", "0.0.0.0", "--port", "8001", "--workers", "4"]
```

### **Production with Gunicorn**
```bash
# Setup production environment
accela-kb env setup production

# Start with Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8001 \
  accela_knowledge_base.api.app:create_app
```

## 🎉 **Benefits**

### **✅ Complete Environment Management**
- Environment-specific configurations
- Automatic validation and error checking
- CLI commands for easy management
- Proper security defaults

### **✅ Production Ready**
- Strong security validations
- Performance optimizations
- Proper logging configuration
- CORS and secret key management

### **✅ Developer Friendly**
- Easy setup with sensible defaults
- Clear validation messages
- Environment detection
- Comprehensive documentation

### **✅ Flexible Deployment**
- Docker support
- Multiple deployment options
- Environment-specific optimizations
- Easy configuration management

The environment system ensures your Accela Knowledge Base is properly configured for any deployment scenario while maintaining security and performance best practices!
