#!/usr/bin/env python3
"""
Vector Database Builder for Accela Knowledge Base
Creates embeddings and builds searchable vector database
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any
import numpy as np
from dataclasses import dataclass
import hashlib

# Vector database and embedding imports
try:
    from sentence_transformers import SentenceTransformer
    import faiss
    import pickle
except ImportError:
    print("Required packages not installed. Run:")
    print("pip install sentence-transformers faiss-cpu")
    exit(1)

@dataclass
class DocumentChunk:
    """Represents a chunk of document for embedding"""
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: np.ndarray = None

class AccelaVectorDB:
    """Vector database for Accela knowledge base"""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """Initialize with embedding model"""
        print(f"Loading embedding model: {model_name}")
        self.model = SentenceTransformer(model_name)
        self.dimension = self.model.get_sentence_embedding_dimension()
        
        # FAISS index
        self.index = faiss.IndexFlatIP(self.dimension)  # Inner product for cosine similarity
        self.documents = []
        self.metadata_store = {}
        
    def chunk_content(self, content: str, chunk_size: int = 500, overlap: int = 100) -> List[str]:
        """Split content into overlapping chunks"""
        words = content.split()
        chunks = []
        
        for i in range(0, len(words), chunk_size - overlap):
            chunk_words = words[i:i + chunk_size]
            chunk = ' '.join(chunk_words)
            if chunk.strip():
                chunks.append(chunk)
                
        return chunks
    
    def create_document_chunks(self, script_metadata: Dict[str, Any]) -> List[DocumentChunk]:
        """Create document chunks from script metadata"""
        chunks = []
        content = script_metadata.get('content', '')
        
        if not content:
            return chunks
            
        # Create different types of chunks
        
        # 1. Full script overview chunk
        overview = f"""
        County: {script_metadata['county']}
        Script Type: {script_metadata['script_type']}
        Module: {script_metadata.get('module', 'unknown')}
        App Type: {script_metadata.get('app_type', 'unknown')}
        Functions: {', '.join(script_metadata.get('functions', []))}
        Dependencies: {', '.join(script_metadata.get('dependencies', []))}
        
        {content[:1000]}...
        """
        
        chunk_id = hashlib.md5(f"{script_metadata['file_path']}_overview".encode()).hexdigest()
        chunks.append(DocumentChunk(
            id=chunk_id,
            content=overview.strip(),
            metadata={
                **script_metadata,
                'chunk_type': 'overview',
                'content': None  # Don't store full content in metadata
            }
        ))
        
        # 2. Function-level chunks
        functions = script_metadata.get('functions', [])
        for func in functions:
            # Extract function definition and surrounding context
            func_pattern = f"function\\s+{func}\\s*\\("
            import re
            match = re.search(func_pattern, content, re.IGNORECASE)
            if match:
                start = max(0, match.start() - 200)
                end = min(len(content), match.end() + 1000)
                func_content = content[start:end]
                
                chunk_id = hashlib.md5(f"{script_metadata['file_path']}_{func}".encode()).hexdigest()
                chunks.append(DocumentChunk(
                    id=chunk_id,
                    content=f"Function: {func}\n\n{func_content}",
                    metadata={
                        **script_metadata,
                        'chunk_type': 'function',
                        'function_name': func,
                        'content': None
                    }
                ))
        
        # 3. Content chunks for large scripts
        if len(content) > 2000:
            content_chunks = self.chunk_content(content)
            for i, chunk_content in enumerate(content_chunks):
                chunk_id = hashlib.md5(f"{script_metadata['file_path']}_chunk_{i}".encode()).hexdigest()
                chunks.append(DocumentChunk(
                    id=chunk_id,
                    content=chunk_content,
                    metadata={
                        **script_metadata,
                        'chunk_type': 'content',
                        'chunk_index': i,
                        'content': None
                    }
                ))
        
        return chunks
    
    def add_documents(self, metadata_file: str = "accela_metadata.json"):
        """Add documents from metadata file to vector database"""
        print(f"Loading metadata from {metadata_file}")
        
        with open(metadata_file, 'r', encoding='utf-8') as f:
            scripts_metadata = json.load(f)
        
        print(f"Processing {len(scripts_metadata)} scripts...")
        
        all_chunks = []
        for script_meta in scripts_metadata:
            chunks = self.create_document_chunks(script_meta)
            all_chunks.extend(chunks)
        
        print(f"Created {len(all_chunks)} document chunks")
        
        # Generate embeddings
        print("Generating embeddings...")
        contents = [chunk.content for chunk in all_chunks]
        embeddings = self.model.encode(contents, show_progress_bar=True)
        
        # Normalize embeddings for cosine similarity
        embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)
        
        # Add to FAISS index
        self.index.add(embeddings.astype('float32'))
        
        # Store documents and metadata
        for i, chunk in enumerate(all_chunks):
            chunk.embedding = embeddings[i]
            self.documents.append(chunk)
            self.metadata_store[chunk.id] = chunk.metadata
            
        print(f"Added {len(all_chunks)} documents to vector database")
    
    def search(self, query: str, k: int = 10, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Search the vector database"""
        # Generate query embedding
        query_embedding = self.model.encode([query])
        query_embedding = query_embedding / np.linalg.norm(query_embedding, axis=1, keepdims=True)
        
        # Search FAISS index
        scores, indices = self.index.search(query_embedding.astype('float32'), k * 2)  # Get more for filtering
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx == -1:  # FAISS returns -1 for empty slots
                continue
                
            doc = self.documents[idx]
            
            # Apply filters if provided
            if filters:
                skip = False
                for filter_key, filter_value in filters.items():
                    if filter_key in doc.metadata:
                        if isinstance(filter_value, list):
                            if doc.metadata[filter_key] not in filter_value:
                                skip = True
                                break
                        else:
                            if doc.metadata[filter_key] != filter_value:
                                skip = True
                                break
                if skip:
                    continue
            
            results.append({
                'content': doc.content,
                'metadata': doc.metadata,
                'score': float(score),
                'id': doc.id
            })
            
            if len(results) >= k:
                break
        
        return results
    
    def save_index(self, index_dir: str = "vector_index"):
        """Save the vector index and metadata"""
        os.makedirs(index_dir, exist_ok=True)
        
        # Save FAISS index
        faiss.write_index(self.index, os.path.join(index_dir, "faiss.index"))
        
        # Save documents and metadata
        with open(os.path.join(index_dir, "documents.pkl"), 'wb') as f:
            pickle.dump(self.documents, f)
            
        with open(os.path.join(index_dir, "metadata.pkl"), 'wb') as f:
            pickle.dump(self.metadata_store, f)
            
        print(f"Vector index saved to {index_dir}")
    
    def load_index(self, index_dir: str = "vector_index"):
        """Load the vector index and metadata"""
        # Load FAISS index
        self.index = faiss.read_index(os.path.join(index_dir, "faiss.index"))
        
        # Load documents and metadata
        with open(os.path.join(index_dir, "documents.pkl"), 'rb') as f:
            self.documents = pickle.load(f)
            
        with open(os.path.join(index_dir, "metadata.pkl"), 'rb') as f:
            self.metadata_store = pickle.load(f)
            
        print(f"Vector index loaded from {index_dir}")

def main():
    """Main execution function"""
    # Initialize vector database
    vector_db = AccelaVectorDB()
    
    # Check if metadata file exists
    if not os.path.exists("accela_metadata.json"):
        print("Metadata file not found. Please run knowledge_base_setup.py first.")
        return
    
    # Add documents to vector database
    vector_db.add_documents()
    
    # Save the index
    vector_db.save_index()
    
    # Test search functionality
    print("\n=== TESTING SEARCH FUNCTIONALITY ===")
    
    test_queries = [
        "email notification scripts",
        "permit issuance workflow",
        "inspection scheduling",
        "address validation functions",
        "building permit automation"
    ]
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        results = vector_db.search(query, k=3)
        
        for i, result in enumerate(results, 1):
            print(f"  {i}. Score: {result['score']:.3f}")
            print(f"     County: {result['metadata']['county']}")
            print(f"     Type: {result['metadata']['script_type']}")
            print(f"     File: {Path(result['metadata']['file_path']).name}")
            print(f"     Content: {result['content'][:100]}...")
            print()

if __name__ == "__main__":
    main()
