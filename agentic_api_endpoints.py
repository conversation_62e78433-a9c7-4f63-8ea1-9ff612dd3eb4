#!/usr/bin/env python3
"""
Enhanced API endpoints with agentic capabilities
Extends the knowledge base API with intelligent agent orchestration
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import uuid
from datetime import datetime

from multi_agent_orchestrator import MultiAgentOrchestrator, OrchestrationRequest
from agentic_graph_system import Acc<PERSON>KnowledgeGraph
from llm_integration_examples import AccelaKnowledgeBaseClient

# Pydantic models for agentic API
class AgenticQueryRequest(BaseModel):
    query: str
    use_case: str
    target_counties: Optional[List[str]] = None
    constraints: Dict[str, Any] = {}
    priority: int = 1

class AgenticQueryResponse(BaseModel):
    request_id: str
    query: str
    best_implementation: Dict[str, Any]
    alternatives: List[Dict[str, Any]]
    synthesis: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str]
    processing_time: float

class ComparisonRequest(BaseModel):
    use_case: str
    counties: List[str]
    criteria: Dict[str, float] = {
        'complexity': 0.3,
        'doc_quality': 0.4,
        'usage_frequency': 0.3
    }

class BestPracticeRequest(BaseModel):
    domain: str  # permits, licenses, inspections, etc.
    min_counties: int = 2
    quality_threshold: float = 0.7

class ImplementationPlanRequest(BaseModel):
    selected_implementation: Dict[str, Any]
    target_environment: str
    timeline_weeks: int = 4

# Global instances
knowledge_graph = None
orchestrator = None
kb_client = None

async def initialize_agentic_system():
    """Initialize the agentic system components"""
    global knowledge_graph, orchestrator, kb_client
    
    print("🤖 Initializing agentic system...")
    
    # Initialize knowledge graph
    knowledge_graph = AccelaKnowledgeGraph()
    
    try:
        knowledge_graph.build_graph_from_metadata()
        print("✅ Knowledge graph built successfully")
    except FileNotFoundError:
        print("❌ Metadata file not found. Please run knowledge_base_setup.py first.")
        raise HTTPException(status_code=500, detail="Knowledge base not initialized")
    
    # Initialize orchestrator
    orchestrator = MultiAgentOrchestrator(knowledge_graph)
    
    # Initialize KB client
    kb_client = AccelaKnowledgeBaseClient()
    knowledge_graph.set_kb_client(kb_client)
    
    print("✅ Agentic system initialized")

def create_agentic_app() -> FastAPI:
    """Create FastAPI app with agentic endpoints"""
    
    app = FastAPI(
        title="Accela Agentic Knowledge Base API",
        description="Multi-agent system for optimal Accela implementation recommendations",
        version="2.0.0"
    )
    
    @app.on_event("startup")
    async def startup_event():
        await initialize_agentic_system()
    
    @app.get("/")
    async def root():
        return {
            "message": "Accela Agentic Knowledge Base API",
            "version": "2.0.0",
            "capabilities": [
                "Multi-agent orchestration",
                "Graph-based knowledge reasoning",
                "Cross-county best practice identification",
                "Intelligent implementation recommendations",
                "Automated solution synthesis"
            ],
            "endpoints": {
                "/agentic/query": "Intelligent query with multi-agent analysis",
                "/agentic/compare": "Advanced county comparison",
                "/agentic/best-practices": "Best practice identification",
                "/agentic/implementation-plan": "Generate implementation plan",
                "/accela/event-prefix-analysis": "Accela event prefix analysis",
                "/accela/module-matrix": "Module vs application type matrix",
                "/accela/naming-insights": "Accela naming convention insights",
                "/graph/stats": "Knowledge graph statistics",
                "/graph/export": "Export graph for visualization"
            }
        }
    
    @app.post("/agentic/query", response_model=AgenticQueryResponse)
    async def agentic_query(request: AgenticQueryRequest):
        """Process query using multi-agent orchestration"""
        
        if not orchestrator:
            raise HTTPException(status_code=500, detail="Agentic system not initialized")
        
        start_time = datetime.now()
        
        # Create orchestration request
        orchestration_request = OrchestrationRequest(
            request_id=str(uuid.uuid4()),
            query=request.query,
            use_case=request.use_case,
            target_counties=request.target_counties,
            constraints=request.constraints,
            priority=request.priority
        )
        
        try:
            # Execute orchestration
            result = await orchestrator.orchestrate(orchestration_request)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            return AgenticQueryResponse(
                request_id=result.request_id,
                query=result.query,
                best_implementation=result.best_implementation,
                alternatives=result.alternatives,
                synthesis=result.synthesis,
                confidence=result.confidence,
                reasoning=result.reasoning,
                recommendations=result.recommendations,
                processing_time=processing_time
            )
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Orchestration failed: {str(e)}")
    
    @app.post("/agentic/compare")
    async def advanced_comparison(request: ComparisonRequest):
        """Advanced comparison using graph analysis"""
        
        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")
        
        try:
            # Get expertise scores for each county in the domain
            expertise_scores = knowledge_graph.get_county_expertise(request.use_case)
            
            # Find best implementations for each county
            county_implementations = {}
            
            for county in request.counties:
                if county in expertise_scores:
                    # Find implementations for this county
                    implementations = knowledge_graph.find_best_implementations(
                        request.use_case, 
                        criteria=request.criteria
                    )
                    
                    # Filter for this county
                    county_impls = [impl for impl in implementations 
                                  if impl.get('county') == county]
                    
                    county_implementations[county] = {
                        'expertise_score': expertise_scores[county],
                        'implementations': county_impls[:3],  # Top 3
                        'implementation_count': len(county_impls)
                    }
            
            # Find common patterns
            patterns = knowledge_graph.find_implementation_patterns("function_combinations")
            
            # Generate recommendations
            recommendations = []
            
            # Find best county
            if expertise_scores:
                best_county = max(expertise_scores.items(), key=lambda x: x[1])
                recommendations.append(f"Primary recommendation: {best_county[0]} (expertise: {best_county[1]:.2f})")
            
            # Pattern recommendations
            if patterns:
                common_pattern = max(patterns.items(), key=lambda x: len(x[1]))
                recommendations.append(f"Common pattern: {common_pattern[0]} (used by {len(common_pattern[1])} counties)")
            
            return {
                "use_case": request.use_case,
                "county_analysis": county_implementations,
                "expertise_scores": expertise_scores,
                "common_patterns": patterns,
                "recommendations": recommendations,
                "criteria_used": request.criteria
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Comparison failed: {str(e)}")
    
    @app.post("/agentic/best-practices")
    async def identify_best_practices(request: BestPracticeRequest):
        """Identify best practices across counties"""
        
        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")
        
        try:
            # Get expertise scores for the domain
            expertise_scores = knowledge_graph.get_county_expertise(request.domain)
            
            # Filter counties that meet quality threshold
            qualified_counties = {county: score for county, score in expertise_scores.items() 
                                if score >= request.quality_threshold}
            
            if len(qualified_counties) < request.min_counties:
                return {
                    "domain": request.domain,
                    "qualified_counties": qualified_counties,
                    "best_practices": [],
                    "message": f"Only {len(qualified_counties)} counties meet quality threshold of {request.quality_threshold}"
                }
            
            # Find best implementations from qualified counties
            best_implementations = knowledge_graph.find_best_implementations(
                request.domain,
                criteria={'complexity': 0.2, 'doc_quality': 0.5, 'usage_frequency': 0.3}
            )
            
            # Filter for qualified counties
            qualified_implementations = [impl for impl in best_implementations 
                                       if impl.get('county') in qualified_counties]
            
            # Extract best practices
            best_practices = []
            
            for impl in qualified_implementations[:5]:  # Top 5
                practice = {
                    'county': impl['county'],
                    'score': impl['score'],
                    'key_functions': impl['metadata'].get('functions', [])[:3],
                    'complexity': impl['metadata'].get('complexity'),
                    'documentation': impl['metadata'].get('doc_quality'),
                    'practice_description': f"Follow {impl['county']}'s approach for {request.domain}",
                    'rationale': impl.get('reasoning', 'High-quality implementation')
                }
                best_practices.append(practice)
            
            # Find common patterns among best practices
            all_functions = []
            for practice in best_practices:
                all_functions.extend(practice['key_functions'])
            
            from collections import Counter
            common_functions = Counter(all_functions).most_common(5)
            
            return {
                "domain": request.domain,
                "qualified_counties": qualified_counties,
                "best_practices": best_practices,
                "common_functions": common_functions,
                "recommendations": [
                    f"Adopt {best_practices[0]['county']}'s approach as primary reference" if best_practices else "No qualified implementations found",
                    f"Standardize on common functions: {', '.join([func for func, count in common_functions[:3]])}" if common_functions else "No common patterns identified"
                ]
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Best practice identification failed: {str(e)}")
    
    @app.post("/agentic/implementation-plan")
    async def generate_implementation_plan(request: ImplementationPlanRequest):
        """Generate detailed implementation plan"""
        
        try:
            implementation = request.selected_implementation
            
            # Extract key information
            county = implementation.get('county', 'Unknown')
            functions = implementation.get('metadata', {}).get('functions', [])
            complexity = implementation.get('metadata', {}).get('complexity', 'medium')
            
            # Generate phases based on complexity and timeline
            phases = []
            weeks_per_phase = max(1, request.timeline_weeks // 4)
            
            # Phase 1: Analysis and Setup
            phases.append({
                'phase': 1,
                'title': 'Analysis and Setup',
                'duration_weeks': weeks_per_phase,
                'tasks': [
                    f'Analyze {county} implementation in detail',
                    'Set up development environment',
                    'Review dependencies and requirements',
                    'Create project structure'
                ],
                'deliverables': [
                    'Implementation analysis document',
                    'Development environment setup',
                    'Project plan'
                ]
            })
            
            # Phase 2: Core Development
            core_functions = functions[:5] if len(functions) > 5 else functions
            phases.append({
                'phase': 2,
                'title': 'Core Development',
                'duration_weeks': weeks_per_phase * 2,
                'tasks': [f'Implement {func}' for func in core_functions] + [
                    'Create unit tests',
                    'Implement error handling',
                    'Add logging and monitoring'
                ],
                'deliverables': [
                    'Core functionality implemented',
                    'Unit tests passing',
                    'Error handling in place'
                ]
            })
            
            # Phase 3: Integration and Testing
            phases.append({
                'phase': 3,
                'title': 'Integration and Testing',
                'duration_weeks': weeks_per_phase,
                'tasks': [
                    'Integration testing',
                    'Performance testing',
                    'Security review',
                    'User acceptance testing'
                ],
                'deliverables': [
                    'Integration tests passing',
                    'Performance benchmarks met',
                    'Security review completed'
                ]
            })
            
            # Phase 4: Deployment and Documentation
            phases.append({
                'phase': 4,
                'title': 'Deployment and Documentation',
                'duration_weeks': weeks_per_phase,
                'tasks': [
                    'Prepare deployment package',
                    'Create user documentation',
                    'Deploy to target environment',
                    'Monitor and validate'
                ],
                'deliverables': [
                    'Deployed solution',
                    'Complete documentation',
                    'Monitoring dashboard'
                ]
            })
            
            # Risk assessment
            risks = []
            if complexity == 'high':
                risks.append({
                    'risk': 'High implementation complexity',
                    'impact': 'High',
                    'mitigation': 'Break down into smaller components, add extra testing'
                })
            
            if len(functions) > 10:
                risks.append({
                    'risk': 'Large number of functions to implement',
                    'impact': 'Medium',
                    'mitigation': 'Prioritize core functions, implement in phases'
                })
            
            # Success criteria
            success_criteria = [
                'All core functions implemented and tested',
                'Performance meets requirements',
                'Documentation complete',
                'Deployed successfully to target environment'
            ]
            
            return {
                'implementation_plan': {
                    'source_county': county,
                    'target_environment': request.target_environment,
                    'total_timeline_weeks': request.timeline_weeks,
                    'phases': phases,
                    'risk_assessment': risks,
                    'success_criteria': success_criteria,
                    'resource_requirements': {
                        'developers': 1 if complexity == 'low' else 2,
                        'testers': 1,
                        'estimated_hours': len(functions) * 8 * (2 if complexity == 'high' else 1)
                    }
                }
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Implementation plan generation failed: {str(e)}")

    @app.get("/accela/event-prefix-analysis")
    async def get_event_prefix_analysis():
        """Get Accela event prefix analysis across counties"""

        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")

        try:
            analysis = knowledge_graph.get_event_prefix_analysis()

            return {
                "event_prefix_analysis": analysis,
                "insights": {
                    "total_prefixes": len(analysis['prefix_usage']),
                    "most_diverse_county": max(analysis['county_prefix_diversity'].items(),
                                             key=lambda x: x[1]['prefix_count'])[0] if analysis['county_prefix_diversity'] else None,
                    "most_common_prefix": analysis['most_common_prefixes'][0][0] if analysis['most_common_prefixes'] else None,
                    "specialized_counties_count": len(analysis['specialized_counties'])
                },
                "recommendations": [
                    f"Consider standardizing on {analysis['most_common_prefixes'][0][0]} pattern" if analysis['most_common_prefixes'] else "No common patterns found",
                    f"Learn from {max(analysis['county_prefix_diversity'].items(), key=lambda x: x[1]['prefix_count'])[0]}'s diverse event handling" if analysis['county_prefix_diversity'] else "No diversity data available"
                ]
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Event prefix analysis failed: {str(e)}")

    @app.get("/accela/module-matrix")
    async def get_module_matrix():
        """Get module vs application type matrix analysis"""

        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")

        try:
            matrix_analysis = knowledge_graph.get_module_application_matrix()

            # Find gaps and opportunities
            gaps = []
            opportunities = []

            matrix = matrix_analysis['module_app_matrix']
            for module, app_types in matrix.items():
                for app_type, counties in app_types.items():
                    if len(counties) == 1:
                        gaps.append(f"{module}→{app_type} only in {counties[0]}")
                    elif len(counties) >= 3:
                        opportunities.append(f"{module}→{app_type} proven in {len(counties)} counties")

            return {
                "matrix_analysis": matrix_analysis,
                "gaps": gaps[:5],  # Top 5 gaps
                "opportunities": opportunities[:5],  # Top 5 opportunities
                "recommendations": [
                    f"Share {opportunities[0].split(' proven')[0]} implementation across counties" if opportunities else "No clear opportunities identified",
                    f"Address gap: {gaps[0]}" if gaps else "No significant gaps identified"
                ]
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Module matrix analysis failed: {str(e)}")

    @app.get("/accela/naming-insights")
    async def get_naming_insights(county: str = None):
        """Get insights about Accela naming convention usage"""

        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")

        try:
            insights = {
                "naming_convention_usage": {},
                "wildcard_usage": {},
                "county_specific_insights": {}
            }

            # Analyze naming convention usage
            total_scripts = 0
            scripts_with_naming = 0
            wildcard_scripts = 0

            for script_node in knowledge_graph.graph.nodes():
                if script_node.startswith("script:"):
                    script_data = knowledge_graph.graph.nodes[script_node]

                    # Filter by county if specified
                    if county and script_data.get('county') != county:
                        continue

                    total_scripts += 1

                    if script_data.get('event_prefix'):
                        scripts_with_naming += 1

                    if script_data.get('is_wildcard'):
                        wildcard_scripts += 1

            insights['naming_convention_usage'] = {
                'total_scripts': total_scripts,
                'scripts_with_naming': scripts_with_naming,
                'naming_coverage_percentage': (scripts_with_naming / total_scripts * 100) if total_scripts > 0 else 0,
                'wildcard_scripts': wildcard_scripts,
                'wildcard_percentage': (wildcard_scripts / total_scripts * 100) if total_scripts > 0 else 0
            }

            # County-specific insights if requested
            if county:
                county_node = f"county:{county}"
                if knowledge_graph.graph.has_node(county_node):
                    county_prefixes = set()
                    county_modules = set()
                    county_app_types = set()

                    for script_node in knowledge_graph.graph.successors(county_node):
                        script_data = knowledge_graph.graph.nodes[script_node]

                        if script_data.get('event_prefix'):
                            county_prefixes.add(script_data['event_prefix'])
                        if script_data.get('module'):
                            county_modules.add(script_data['module'])
                        if script_data.get('application_type'):
                            county_app_types.add(script_data['application_type'])

                    insights['county_specific_insights'] = {
                        'unique_event_prefixes': list(county_prefixes),
                        'unique_modules': list(county_modules),
                        'unique_application_types': list(county_app_types),
                        'diversity_score': len(county_prefixes) + len(county_modules) + len(county_app_types)
                    }

            return {
                "insights": insights,
                "recommendations": [
                    f"Naming convention coverage: {insights['naming_convention_usage']['naming_coverage_percentage']:.1f}%",
                    f"Wildcard usage: {insights['naming_convention_usage']['wildcard_percentage']:.1f}% (lower is more specific)",
                    "Consider standardizing naming conventions across all scripts" if insights['naming_convention_usage']['naming_coverage_percentage'] < 80 else "Good naming convention coverage"
                ]
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Naming insights analysis failed: {str(e)}")

    @app.get("/graph/stats")
    async def get_graph_stats():
        """Get knowledge graph statistics"""
        
        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")
        
        try:
            stats = knowledge_graph.get_graph_stats()
            return {
                "graph_statistics": stats,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to get graph stats: {str(e)}")
    
    @app.post("/graph/export")
    async def export_graph(background_tasks: BackgroundTasks, format: str = "gexf"):
        """Export knowledge graph for visualization"""
        
        if not knowledge_graph:
            raise HTTPException(status_code=500, detail="Knowledge graph not initialized")
        
        try:
            filename = f"accela_knowledge_graph.{format}"
            
            if format == "gexf":
                background_tasks.add_task(knowledge_graph.export_graph, filename)
            else:
                raise HTTPException(status_code=400, detail=f"Unsupported format: {format}")
            
            return {
                "message": f"Graph export started",
                "filename": filename,
                "format": format
            }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Graph export failed: {str(e)}")
    
    return app

# Create the app instance
app = create_agentic_app()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
