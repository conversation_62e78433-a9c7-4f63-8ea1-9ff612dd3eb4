#!/usr/bin/env python3
"""
Debug markdown formatter to find the issue
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accela_knowledge_base.core.config import Config
from accela_knowledge_base.knowledge_graph.graph_builder import AccelaKnowledgeGraph
from accela_knowledge_base.agents.orchestrator import MultiAgentOrchestrator
from accela_knowledge_base.core.models import OrchestrationRequest
from accela_knowledge_base.core.query_processor import NaturalLanguageQueryProcessor, MarkdownFormatter


def debug_markdown():
    """Debug the markdown formatter"""
    
    print("🔍 Debugging Markdown Formatter")
    print("=" * 50)
    
    try:
        # Initialize components
        config = Config.from_env()
        knowledge_graph = AccelaKnowledgeGraph(config)
        knowledge_graph.build_from_metadata()
        
        orchestrator = MultiAgentOrchestrator(knowledge_graph, config)
        
        # Create test request
        request = OrchestrationRequest(
            request_id="test-123",
            query="How does Asheville handle building permit email notifications?",
            use_case="notification",
            target_counties=["asheville"]
        )
        
        print(f"📝 Request: {request.query}")
        
        # Run orchestration
        print("🚀 Running orchestration...")
        import asyncio
        result = asyncio.run(orchestrator.orchestrate(request))
        
        print("✅ Orchestration completed!")
        
        # Process query
        print("🔤 Processing natural language query...")
        query_processor = NaturalLanguageQueryProcessor()
        processed_query = query_processor.process_query(request.query)
        
        print("✅ Query processed!")
        
        # Test markdown formatting
        print("📝 Testing markdown formatting...")
        formatter = MarkdownFormatter()
        
        try:
            markdown_output = formatter.format_response(processed_query, result)
            print("✅ Markdown formatting successful!")
            print(f"📄 Output length: {len(markdown_output)} characters")
            
            # Save output
            with open("debug_markdown_output.md", "w") as f:
                f.write(markdown_output)
            print("💾 Markdown saved to debug_markdown_output.md")
            
            return True
            
        except Exception as e:
            print(f"❌ Markdown formatting failed: {e}")
            import traceback
            traceback.print_exc()
            
            # Let's try to isolate the issue
            print("\n🔍 Isolating the issue...")
            
            # Test individual components
            print("Testing basic data access...")
            
            try:
                best = result.best_implementation
                print(f"✅ Best implementation: {type(best)}")
                
                if isinstance(best, dict):
                    county = best.get('county', 'Unknown')
                    print(f"✅ County: {county}")
                else:
                    print(f"❌ Best implementation is not dict: {best}")
                
                alternatives = result.alternatives
                print(f"✅ Alternatives: {type(alternatives)}")
                
                if isinstance(alternatives, list):
                    print(f"✅ Alternatives count: {len(alternatives)}")
                    for i, alt in enumerate(alternatives):
                        print(f"  Alt {i}: {type(alt)}")
                        if isinstance(alt, dict):
                            print(f"    County: {alt.get('county', 'Unknown')}")
                        else:
                            print(f"    ❌ Alt {i} is not dict: {alt}")
                else:
                    print(f"❌ Alternatives is not list: {alternatives}")
                
            except Exception as e2:
                print(f"❌ Data access failed: {e2}")
                import traceback
                traceback.print_exc()
            
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = debug_markdown()
    if success:
        print("\n🎉 Markdown debugging completed successfully!")
    else:
        print("\n💥 Markdown debugging failed!")
