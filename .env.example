# Accela Knowledge Base Configuration
# Copy this file to .env and configure your settings

# Core paths
ACCELA_SRC_DIRECTORY=src
ACCELA_METADATA_FILE=accela_metadata.json
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf

# API settings
ACCELA_API_HOST=0.0.0.0
ACCELA_API_PORT=8001
ACCELA_API_WORKERS=4
ACCELA_API_SECRET_KEY=change-this-secret-key-in-production
ACCELA_API_CORS_ORIGINS=*

# LLM configuration (optional - system works without this)
OPENAI_API_KEY=
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300
ACCELA_LLM_MAX_TOKENS_REASONING=150
ACCELA_LLM_TEMPERATURE=0.1

# Agent settings
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=2
ACCELA_SIMILARITY_THRESHOLD=0.3
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.4

# Performance
ACCELA_MAX_CODE_LENGTH_FOR_LLM=2000
ACCELA_MAX_SEARCH_RESULTS=20
ACCELA_CACHE_ENABLED=true

# Logging
ACCELA_LOG_LEVEL=INFO
ACCELA_LOG_FILE=logs/accela_kb.log

# Development
ACCELA_DEBUG=false
ACCELA_RELOAD=false
