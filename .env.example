# Accela Knowledge Base Environment Configuration
# Copy this file to .env and configure your settings

# =============================================================================
# CORE CONFIGURATION
# =============================================================================

# Data paths
ACCELA_SRC_DIRECTORY=src
ACCELA_METADATA_FILE=accela_metadata.json
ACCELA_GRAPH_EXPORT_FILE=accela_knowledge_graph.gexf

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API server settings
ACCELA_API_HOST=0.0.0.0
ACCELA_API_PORT=8001
ACCELA_API_WORKERS=4

# API security (set in production)
ACCELA_API_SECRET_KEY=your-secret-key-here-change-in-production
ACCELA_API_CORS_ORIGINS=*

# =============================================================================
# LLM CONFIGURATION (OPTIONAL)
# =============================================================================

# OpenAI API key for enhanced intelligence (optional)
# System works perfectly without this - only adds semantic analysis
OPENAI_API_KEY=

# LLM model settings
ACCELA_LLM_MODEL=gpt-3.5-turbo
ACCELA_LLM_MAX_TOKENS_ANALYSIS=300
ACCELA_LLM_MAX_TOKENS_REASONING=150
ACCELA_LLM_TEMPERATURE=0.1

# =============================================================================
# AGENT CONFIGURATION
# =============================================================================

# Agent behavior settings
ACCELA_ANALYSIS_RELEVANCE_THRESHOLD=2
ACCELA_SIMILARITY_THRESHOLD=0.3
ACCELA_FUNCTION_SIMILARITY_THRESHOLD=0.4

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Performance optimization
ACCELA_MAX_CODE_LENGTH_FOR_LLM=2000
ACCELA_MAX_SEARCH_RESULTS=20
ACCELA_CACHE_ENABLED=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Logging settings
ACCELA_LOG_LEVEL=INFO
ACCELA_LOG_FILE=logs/accela_kb.log
ACCELA_LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# =============================================================================
# DATABASE CONFIGURATION (FUTURE)
# =============================================================================

# Database settings (for future enhancements)
# ACCELA_DATABASE_URL=sqlite:///accela_kb.db
# ACCELA_REDIS_URL=redis://localhost:6379/0

# =============================================================================
# MONITORING & OBSERVABILITY (PRODUCTION)
# =============================================================================

# Monitoring settings
# ACCELA_METRICS_ENABLED=true
# ACCELA_HEALTH_CHECK_INTERVAL=30
# ACCELA_PROMETHEUS_PORT=9090

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development mode
ACCELA_DEBUG=false
ACCELA_RELOAD=false

# Testing
ACCELA_TEST_MODE=false
ACCELA_TEST_DATA_PATH=tests/data
