#!/usr/bin/env python3
"""
Debug orchestration to find the issue
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accela_knowledge_base.core.config import Config
from accela_knowledge_base.knowledge_graph.graph_builder import AccelaKnowledgeGraph
from accela_knowledge_base.agents.orchestrator import MultiAgentOrchestrator
from accela_knowledge_base.core.models import OrchestrationRequest


def debug_orchestration():
    """Debug the orchestration process"""
    
    print("🔍 Debugging Orchestration Process")
    print("=" * 50)
    
    try:
        # Initialize components
        config = Config.from_env()
        knowledge_graph = AccelaKnowledgeGraph(config)
        knowledge_graph.build_from_metadata()
        
        orchestrator = MultiAgentOrchestrator(knowledge_graph, config)
        
        # Create test request
        request = OrchestrationRequest(
            request_id="test-123",
            query="How does Asheville handle building permit email notifications?",
            use_case="notification",
            target_counties=["asheville"]
        )
        
        print(f"📝 Request: {request.query}")
        print(f"🎯 Use case: {request.use_case}")
        print(f"📍 Counties: {request.target_counties}")
        
        # Run orchestration
        print("\n🚀 Running orchestration...")
        import asyncio
        result = asyncio.run(orchestrator.orchestrate(request))
        
        print("✅ Orchestration completed!")
        print(f"📊 Confidence: {result.confidence}")
        print(f"⏱️ Processing time: {result.processing_time:.2f}s")
        
        # Debug result structure
        print("\n🔍 Result Structure Analysis:")
        print(f"- Request ID: {result.request_id}")
        print(f"- Query: {result.query}")
        print(f"- Best implementation type: {type(result.best_implementation)}")
        print(f"- Best implementation: {result.best_implementation}")
        print(f"- Alternatives type: {type(result.alternatives)}")
        print(f"- Alternatives count: {len(result.alternatives) if result.alternatives else 0}")
        
        if result.alternatives:
            print(f"- First alternative type: {type(result.alternatives[0])}")
            print(f"- First alternative: {result.alternatives[0]}")
        
        print(f"- Synthesis type: {type(result.synthesis)}")
        print(f"- Reasoning: {result.reasoning}")
        print(f"- Recommendations: {result.recommendations}")
        
        # Test accessing data like the markdown formatter does
        print("\n🧪 Testing Data Access:")
        
        if result.best_implementation:
            print("✅ Best implementation exists")
            if isinstance(result.best_implementation, dict):
                print("✅ Best implementation is dict")
                county = result.best_implementation.get('county', 'Unknown')
                print(f"✅ County: {county}")
                score = result.best_implementation.get('score', 0)
                print(f"✅ Score: {score}")
                metadata = result.best_implementation.get('metadata', {})
                print(f"✅ Metadata type: {type(metadata)}")
                if isinstance(metadata, dict):
                    functions = metadata.get('functions', [])
                    print(f"✅ Functions: {functions[:3]}")
            else:
                print(f"❌ Best implementation is not dict: {type(result.best_implementation)}")
        
        if result.alternatives:
            print("✅ Alternatives exist")
            for i, alt in enumerate(result.alternatives[:2]):
                print(f"  Alternative {i+1} type: {type(alt)}")
                if isinstance(alt, dict):
                    print(f"  ✅ Alternative {i+1} county: {alt.get('county', 'Unknown')}")
                else:
                    print(f"  ❌ Alternative {i+1} is not dict: {type(alt)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = debug_orchestration()
    if success:
        print("\n🎉 Orchestration debugging completed successfully!")
    else:
        print("\n💥 Orchestration debugging failed!")
