#!/usr/bin/env python3
"""
Test script for natural language queries with markdown output
"""

import requests
import json
import time
from pathlib import Path


def test_query(query, counties=None, save_html=True):
    """Test a natural language query"""
    
    print(f"\n🎯 Testing Query: {query}")
    if counties:
        print(f"📍 Counties: {counties}")
    
    start_time = time.time()
    
    try:
        # Test the simple /ask endpoint
        payload = {"query": query}
        if counties:
            payload["counties"] = counties
        
        response = requests.post('http://localhost:8001/agentic/ask', json=payload)
        
        processing_time = time.time() - start_time
        
        if response.status_code == 200:
            print(f"✅ Success! ({processing_time:.2f}s)")
            
            if save_html:
                # Create filename from query
                filename = query.lower().replace(' ', '_').replace('?', '').replace(',', '')[:50] + '.html'
                
                with open(filename, 'w') as f:
                    f.write(response.text)
                
                print(f"📄 HTML saved to: {filename}")
                print(f"🌐 Open in browser: file://{Path(filename).absolute()}")
            
            return True
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def test_json_endpoint(query, counties=None):
    """Test the JSON endpoint with markdown output"""
    
    print(f"\n📊 Testing JSON Endpoint: {query}")
    
    try:
        payload = {
            "query": query,
            "output_format": "markdown"
        }
        if counties:
            payload["target_counties"] = counties.split(', ')
        
        response = requests.post('http://localhost:8001/agentic/query', json=payload)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ JSON Success!")
            print(f"📊 Confidence: {result['confidence']:.1%}")
            print(f"⏱️ Processing Time: {result['processing_time']:.2f}s")
            
            if result.get('markdown_output'):
                print(f"📝 Markdown generated ({len(result['markdown_output'])} chars)")
                
                # Save markdown
                filename = f"markdown_{query.lower().replace(' ', '_')[:30]}.md"
                with open(filename, 'w') as f:
                    f.write(result['markdown_output'])
                print(f"📄 Markdown saved to: {filename}")
            
            return True
        else:
            print(f"❌ Error {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def main():
    """Run comprehensive tests"""
    
    print("🚀 Testing Natural Language Query System")
    print("=" * 60)
    
    # Test cases
    test_cases = [
        {
            "query": "How does Asheville handle building permit email notifications?",
            "counties": "asheville"
        },
        {
            "query": "Compare Asheville and Santa Barbara building permit workflows",
            "counties": "asheville, santa barbara"
        },
        {
            "query": "What are the differences between Marin and Alameda inspection scheduling?",
            "counties": "marin, alameda"
        },
        {
            "query": "Show me fee calculation implementations across all counties",
            "counties": None
        },
        {
            "query": "How do counties handle license renewal notifications?",
            "counties": None
        },
        {
            "query": "Find the best implementation for automatic inspection assignment",
            "counties": None
        },
        {
            "query": "Compare document generation approaches in Santa Barbara vs Sonoma",
            "counties": "santa_barbara, sonoma"
        },
        {
            "query": "What's the difference in zoning compliance checking between counties?",
            "counties": None
        }
    ]
    
    print(f"🧪 Running {len(test_cases)} test cases...")
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"Test Case {i}/{len(test_cases)}")
        
        # Test HTML endpoint
        if test_query(test_case["query"], test_case["counties"]):
            success_count += 1
        
        # Test JSON endpoint with markdown
        test_json_endpoint(test_case["query"], test_case["counties"])
        
        # Small delay between tests
        time.sleep(1)
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {success_count}/{len(test_cases)} successful")
    
    if success_count == len(test_cases):
        print("🎉 All tests passed! Natural language query system is working perfectly!")
    else:
        print("⚠️ Some tests failed. Check the error messages above.")
    
    print(f"\n📁 Generated files:")
    html_files = list(Path('.').glob('*.html'))
    md_files = list(Path('.').glob('markdown_*.md'))
    
    for file in html_files:
        print(f"   🌐 {file}")
    for file in md_files:
        print(f"   📝 {file}")
    
    print(f"\n🚀 API Endpoints Available:")
    print(f"   • Simple Query: POST http://localhost:8001/agentic/ask")
    print(f"   • JSON with Markdown: POST http://localhost:8001/agentic/query")
    print(f"   • HTML Response: POST http://localhost:8001/agentic/query/markdown")
    print(f"   • API Docs: http://localhost:8001/docs")


if __name__ == "__main__":
    main()
