#!/usr/bin/env python3
"""
Agentic Graph-Based Knowledge System for Accela Implementations
Uses intelligent agents and graph knowledge to find optimal solutions across counties
"""

import networkx as nx
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
import requests
from abc import ABC, abstractmethod

class AgentRole(Enum):
    """Different types of agents in the system"""
    ANALYZER = "analyzer"           # Analyzes code patterns and relationships
    COMPARATOR = "comparator"       # Compares implementations across counties
    RECOMMENDER = "recommender"     # Recommends best practices
    SYNTHESIZER = "synthesizer"     # Synthesizes solutions from multiple sources
    VALIDATOR = "validator"         # Validates recommendations

@dataclass
class KnowledgeNode:
    """Represents a node in the knowledge graph"""
    id: str
    type: str  # county, function, script_type, module, pattern
    properties: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class KnowledgeEdge:
    """Represents an edge in the knowledge graph"""
    source: str
    target: str
    relationship: str  # implements, uses, similar_to, depends_on, best_practice
    weight: float = 1.0
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentTask:
    """Task for an agent to execute"""
    task_id: str
    agent_role: AgentRole
    query: str
    context: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1

@dataclass
class AgentResponse:
    """Response from an agent"""
    task_id: str
    agent_role: AgentRole
    result: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str] = field(default_factory=list)

class AccelaKnowledgeGraph:
    """Graph-based knowledge representation for Accela implementations"""
    
    def __init__(self):
        self.graph = nx.MultiDiGraph()
        self.kb_client = None
        
    def set_kb_client(self, client):
        """Set the knowledge base client"""
        self.kb_client = client
        
    def build_graph_from_metadata(self, metadata_file: str = "accela_metadata.json"):
        """Build enhanced knowledge graph from metadata with Accela naming convention"""
        print("🔗 Building enhanced knowledge graph with Accela naming convention...")

        with open(metadata_file, 'r') as f:
            scripts_data = json.load(f)

        # Track various node types
        counties = set()
        functions = set()
        modules = set()
        event_prefixes = set()
        application_types = set()

        for script in scripts_data:
            county = script['county']
            counties.add(county)

            # Add county node
            if not self.graph.has_node(f"county:{county}"):
                self.graph.add_node(f"county:{county}",
                                  type="county",
                                  name=county,
                                  script_count=0,
                                  complexity_avg=0)

            # Update county stats
            self.graph.nodes[f"county:{county}"]['script_count'] += 1

            # Parse naming convention
            naming = script.get('naming_convention', {})
            event_prefix = naming.get('event_prefix') if naming else None
            module = naming.get('module') if naming else None
            app_type = naming.get('application_type') if naming else None
            sub_type = naming.get('sub_type') if naming else None
            category = naming.get('category') if naming else None
            is_wildcard = naming.get('is_wildcard', False) if naming else False

            # Add script node with enhanced metadata
            script_id = f"script:{script['file_path']}"
            self.graph.add_node(script_id,
                              type="script",
                              county=county,
                              script_type=script['script_type'],
                              event_prefix=event_prefix,
                              module=module,
                              application_type=app_type,
                              sub_type=sub_type,
                              category=category,
                              is_wildcard=is_wildcard,
                              complexity=script['complexity'],
                              doc_quality=script['documentation_quality'],
                              functions=script.get('functions', []),
                              legacy_module=script.get('module'),  # For backward compatibility
                              legacy_app_type=script.get('app_type'))

            # Connect county to script
            self.graph.add_edge(f"county:{county}", script_id, relationship="contains")

            # Add event prefix nodes and relationships
            if event_prefix:
                event_prefixes.add(event_prefix)
                prefix_id = f"event_prefix:{event_prefix}"

                if not self.graph.has_node(prefix_id):
                    self.graph.add_node(prefix_id,
                                      type="event_prefix",
                                      name=event_prefix,
                                      usage_count=0,
                                      counties=set(),
                                      description=self._get_event_prefix_description(event_prefix))

                self.graph.nodes[prefix_id]['usage_count'] += 1
                self.graph.nodes[prefix_id]['counties'].add(county)
                self.graph.add_edge(script_id, prefix_id, relationship="uses_event_prefix")

            # Add module nodes (from naming convention)
            if module:
                modules.add(module)
                module_id = f"accela_module:{module}"

                if not self.graph.has_node(module_id):
                    self.graph.add_node(module_id,
                                      type="accela_module",
                                      name=module,
                                      usage_count=0,
                                      counties=set())

                self.graph.nodes[module_id]['usage_count'] += 1
                self.graph.nodes[module_id]['counties'].add(county)
                self.graph.add_edge(script_id, module_id, relationship="targets_module")

            # Add application type nodes
            if app_type:
                application_types.add(app_type)
                app_type_id = f"app_type:{app_type}"

                if not self.graph.has_node(app_type_id):
                    self.graph.add_node(app_type_id,
                                      type="application_type",
                                      name=app_type,
                                      usage_count=0,
                                      counties=set())

                self.graph.nodes[app_type_id]['usage_count'] += 1
                self.graph.nodes[app_type_id]['counties'].add(county)
                self.graph.add_edge(script_id, app_type_id, relationship="handles_app_type")

            # Add function nodes and relationships
            for func in script.get('functions', []):
                func_id = f"function:{func}"
                functions.add(func)

                if not self.graph.has_node(func_id):
                    self.graph.add_node(func_id,
                                      type="function",
                                      name=func,
                                      usage_count=0,
                                      counties=set(),
                                      event_prefixes=set(),
                                      modules=set())

                # Update function stats
                self.graph.nodes[func_id]['usage_count'] += 1
                self.graph.nodes[func_id]['counties'].add(county)
                if event_prefix:
                    self.graph.nodes[func_id]['event_prefixes'].add(event_prefix)
                if module:
                    self.graph.nodes[func_id]['modules'].add(module)

                # Connect script to function
                self.graph.add_edge(script_id, func_id, relationship="implements")

            # Legacy module support
            legacy_module = script.get('module')
            if legacy_module and legacy_module != module:
                legacy_module_id = f"legacy_module:{legacy_module}"
                if not self.graph.has_node(legacy_module_id):
                    self.graph.add_node(legacy_module_id, type="legacy_module", name=legacy_module)
                self.graph.add_edge(script_id, legacy_module_id, relationship="belongs_to")

        # Calculate enhanced similarity relationships
        self._calculate_enhanced_similarities()

        print(f"✅ Enhanced graph built: {self.graph.number_of_nodes()} nodes, {self.graph.number_of_edges()} edges")
        print(f"   Counties: {len(counties)}, Functions: {len(functions)}")
        print(f"   Event Prefixes: {len(event_prefixes)}, Modules: {len(modules)}")
        print(f"   Application Types: {len(application_types)}")

    def _get_event_prefix_description(self, prefix: str) -> str:
        """Get description for event prefix"""
        descriptions = {
            'ASA': 'Application Submittal After',
            'ASB': 'Application Submittal Before',
            'ASIUA': 'Application Submittal In Use After',
            'ASUA': 'Application Status Update After',
            'ASUB': 'Application Status Update Before',
            'CTRCA': 'Convert To Real Cap After',
            'WTUA': 'Workflow Task Update After',
            'WTUB': 'Workflow Task Update Before',
            'ISA': 'Inspection Schedule After',
            'ISB': 'Inspection Schedule Before',
            'IRSA': 'Inspection Result Submit After',
            'IRSB': 'Inspection Result Submit Before',
            'IFA': 'Inspection Finalize After',
            'PRA': 'Payment Receive After',
            'PIA': 'Payment Invoice After',
            'FAA': 'Fee Assess After',
            'ICA': 'Invoice Create After',
            'LLSA': 'License Lookup Submit After',
            'CAA': 'Condition Approve After',
            'CEA': 'Condition Edit After',
            'CAEC': 'Condition Approve Edit Create',
            'RLPAA': 'Reference Licensed Professional Add After'
        }
        return descriptions.get(prefix, f"Unknown event prefix: {prefix}")
        
    def _calculate_enhanced_similarities(self):
        """Calculate enhanced similarity relationships using Accela naming convention"""

        # County similarities based on multiple factors
        counties = [n for n in self.graph.nodes() if n.startswith("county:")]

        for i, county1 in enumerate(counties):
            for county2 in counties[i+1:]:
                similarity = self._calculate_enhanced_county_similarity(county1, county2)
                if similarity > 0.3:  # Threshold for similarity
                    self.graph.add_edge(county1, county2,
                                      relationship="similar_to",
                                      weight=similarity)

        # Event prefix similarities (counties using similar event patterns)
        event_prefixes = [n for n in self.graph.nodes() if n.startswith("event_prefix:")]

        for prefix_node in event_prefixes:
            prefix_counties = self.graph.nodes[prefix_node].get('counties', set())
            if len(prefix_counties) > 1:
                # Connect counties that use the same event prefix
                county_list = list(prefix_counties)
                for i, county1 in enumerate(county_list):
                    for county2 in county_list[i+1:]:
                        county1_node = f"county:{county1}"
                        county2_node = f"county:{county2}"

                        # Add or strengthen relationship
                        if self.graph.has_edge(county1_node, county2_node):
                            current_weight = self.graph[county1_node][county2_node].get('weight', 0)
                            self.graph[county1_node][county2_node]['weight'] = current_weight + 0.1
                        else:
                            self.graph.add_edge(county1_node, county2_node,
                                              relationship="shares_event_patterns",
                                              weight=0.1)

        # Module-based similarities
        modules = [n for n in self.graph.nodes() if n.startswith("accela_module:")]

        for module_node in modules:
            module_counties = self.graph.nodes[module_node].get('counties', set())
            if len(module_counties) > 1:
                # Connect counties that work with the same modules
                county_list = list(module_counties)
                for i, county1 in enumerate(county_list):
                    for county2 in county_list[i+1:]:
                        county1_node = f"county:{county1}"
                        county2_node = f"county:{county2}"

                        if self.graph.has_edge(county1_node, county2_node):
                            current_weight = self.graph[county1_node][county2_node].get('weight', 0)
                            self.graph[county1_node][county2_node]['weight'] = current_weight + 0.15
                        else:
                            self.graph.add_edge(county1_node, county2_node,
                                              relationship="shares_modules",
                                              weight=0.15)

        # Function similarities based on co-occurrence and context
        functions = [n for n in self.graph.nodes() if n.startswith("function:")]

        for i, func1 in enumerate(functions):
            for func2 in functions[i+1:]:
                similarity = self._calculate_enhanced_function_similarity(func1, func2)
                if similarity > 0.4:
                    self.graph.add_edge(func1, func2,
                                      relationship="often_used_with",
                                      weight=similarity)
    
    def _calculate_enhanced_county_similarity(self, county1: str, county2: str) -> float:
        """Calculate enhanced similarity between counties using multiple factors"""

        # Get various attributes for each county
        county1_data = self._get_county_attributes(county1)
        county2_data = self._get_county_attributes(county2)

        # Calculate similarity components
        function_similarity = self._jaccard_similarity(
            county1_data['functions'], county2_data['functions']
        )

        event_prefix_similarity = self._jaccard_similarity(
            county1_data['event_prefixes'], county2_data['event_prefixes']
        )

        module_similarity = self._jaccard_similarity(
            county1_data['modules'], county2_data['modules']
        )

        app_type_similarity = self._jaccard_similarity(
            county1_data['app_types'], county2_data['app_types']
        )

        # Weighted combination
        total_similarity = (
            function_similarity * 0.4 +
            event_prefix_similarity * 0.3 +
            module_similarity * 0.2 +
            app_type_similarity * 0.1
        )

        return total_similarity

    def _get_county_attributes(self, county_node: str) -> Dict[str, set]:
        """Get all attributes for a county"""
        attributes = {
            'functions': set(),
            'event_prefixes': set(),
            'modules': set(),
            'app_types': set()
        }

        # Get all scripts for this county
        for script in self.graph.successors(county_node):
            if script.startswith("script:"):
                script_data = self.graph.nodes[script]

                # Functions
                for func in script_data.get('functions', []):
                    attributes['functions'].add(func)

                # Event prefixes
                if script_data.get('event_prefix'):
                    attributes['event_prefixes'].add(script_data['event_prefix'])

                # Modules
                if script_data.get('module'):
                    attributes['modules'].add(script_data['module'])

                # Application types
                if script_data.get('application_type'):
                    attributes['app_types'].add(script_data['application_type'])

        return attributes

    def _jaccard_similarity(self, set1: set, set2: set) -> float:
        """Calculate Jaccard similarity between two sets"""
        if not set1 and not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0
    
    def _calculate_enhanced_function_similarity(self, func1: str, func2: str) -> float:
        """Calculate enhanced similarity between functions based on context"""

        # Get scripts that use each function
        func1_scripts = set(self.graph.predecessors(func1))
        func2_scripts = set(self.graph.predecessors(func2))

        if not func1_scripts or not func2_scripts:
            return 0.0

        # Basic co-occurrence similarity
        co_occurrence = self._jaccard_similarity(func1_scripts, func2_scripts)

        # Context similarity (same event prefixes, modules, etc.)
        func1_data = self.graph.nodes[func1]
        func2_data = self.graph.nodes[func2]

        event_prefix_similarity = self._jaccard_similarity(
            func1_data.get('event_prefixes', set()),
            func2_data.get('event_prefixes', set())
        )

        module_similarity = self._jaccard_similarity(
            func1_data.get('modules', set()),
            func2_data.get('modules', set())
        )

        county_similarity = self._jaccard_similarity(
            func1_data.get('counties', set()),
            func2_data.get('counties', set())
        )

        # Weighted combination
        total_similarity = (
            co_occurrence * 0.5 +
            event_prefix_similarity * 0.2 +
            module_similarity * 0.2 +
            county_similarity * 0.1
        )

        return total_similarity
    
    def find_best_implementations(self, use_case: str, criteria: Dict[str, float] = None) -> List[Dict]:
        """Find best implementations for a specific use case"""
        
        if criteria is None:
            criteria = {
                'complexity': 0.3,      # Lower complexity preferred
                'doc_quality': 0.4,     # Higher doc quality preferred
                'usage_frequency': 0.3   # Higher usage preferred
            }
        
        # Search for relevant scripts using semantic search
        if self.kb_client:
            search_results = self.kb_client.semantic_search(use_case, k=20)
        else:
            search_results = []
        
        # Score implementations
        scored_implementations = []
        
        for result in search_results:
            script_path = result['metadata']['file_path']
            script_id = f"script:{script_path}"
            
            if self.graph.has_node(script_id):
                score = self._score_implementation(script_id, criteria)
                
                scored_implementations.append({
                    'script_id': script_id,
                    'county': result['metadata']['county'],
                    'score': score,
                    'content': result['content'],
                    'metadata': result['metadata'],
                    'reasoning': self._explain_score(script_id, criteria, score)
                })
        
        # Sort by score
        scored_implementations.sort(key=lambda x: x['score'], reverse=True)
        
        return scored_implementations
    
    def _score_implementation(self, script_id: str, criteria: Dict[str, float]) -> float:
        """Score an implementation based on criteria"""
        
        if not self.graph.has_node(script_id):
            return 0.0
        
        node_data = self.graph.nodes[script_id]
        score = 0.0
        
        # Complexity score (lower is better)
        complexity_map = {'low': 1.0, 'medium': 0.6, 'high': 0.2}
        complexity_score = complexity_map.get(node_data.get('complexity', 'medium'), 0.6)
        score += criteria.get('complexity', 0.3) * complexity_score
        
        # Documentation quality score
        doc_map = {'excellent': 1.0, 'good': 0.7, 'poor': 0.3}
        doc_score = doc_map.get(node_data.get('doc_quality', 'poor'), 0.3)
        score += criteria.get('doc_quality', 0.4) * doc_score
        
        # Usage frequency (based on function popularity)
        usage_score = 0.0
        functions = node_data.get('functions', [])
        if functions:
            for func in functions:
                func_id = f"function:{func}"
                if self.graph.has_node(func_id):
                    func_usage = self.graph.nodes[func_id].get('usage_count', 0)
                    usage_score += func_usage
            usage_score = min(usage_score / len(functions) / 10, 1.0)  # Normalize
        
        score += criteria.get('usage_frequency', 0.3) * usage_score
        
        return score
    
    def _explain_score(self, script_id: str, criteria: Dict[str, float], score: float) -> str:
        """Explain why a script received its score"""
        
        node_data = self.graph.nodes[script_id]
        explanations = []
        
        explanations.append(f"Overall Score: {score:.2f}")
        explanations.append(f"Complexity: {node_data.get('complexity', 'unknown')}")
        explanations.append(f"Documentation: {node_data.get('doc_quality', 'unknown')}")
        explanations.append(f"Functions: {len(node_data.get('functions', []))}")
        
        return " | ".join(explanations)
    
    def get_county_expertise(self, domain: str) -> Dict[str, float]:
        """Get enhanced expertise scores for each county in a specific domain using Accela naming"""

        expertise_scores = {}

        for county_node in self.graph.nodes():
            if not county_node.startswith("county:"):
                continue

            county = county_node.split(":")[1]
            score = 0.0
            script_count = 0

            # Analyze scripts in this domain
            for script_node in self.graph.successors(county_node):
                script_data = self.graph.nodes[script_node]

                # Enhanced domain matching using Accela naming convention
                is_relevant = False

                # Check module from naming convention
                if script_data.get('module') and domain.lower() in script_data['module'].lower():
                    is_relevant = True

                # Check application type
                if script_data.get('application_type') and domain.lower() in script_data['application_type'].lower():
                    is_relevant = True

                # Check legacy fields
                if (domain.lower() in str(script_data.get('legacy_module', '')).lower() or
                    domain.lower() in str(script_data.get('script_type', '')).lower()):
                    is_relevant = True

                if is_relevant:
                    script_count += 1

                    # Enhanced scoring based on multiple factors
                    complexity_bonus = {'low': 0.3, 'medium': 0.2, 'high': 0.1}
                    doc_bonus = {'excellent': 0.4, 'good': 0.2, 'poor': 0.1}

                    base_score = complexity_bonus.get(script_data.get('complexity', 'medium'), 0.2)
                    base_score += doc_bonus.get(script_data.get('doc_quality', 'poor'), 0.1)

                    # Bonus for specific event prefixes that indicate expertise
                    event_prefix = script_data.get('event_prefix')
                    if event_prefix:
                        # Higher score for more complex event handling
                        prefix_bonus = {
                            'ASA': 0.1,   # Application handling
                            'WTUA': 0.15, # Workflow expertise
                            'IRSA': 0.12, # Inspection expertise
                            'CTRCA': 0.08 # Conversion handling
                        }
                        base_score += prefix_bonus.get(event_prefix, 0.05)

                    # Bonus for non-wildcard implementations (more specific)
                    if not script_data.get('is_wildcard', False):
                        base_score += 0.1

                    score += base_score

            if script_count > 0:
                expertise_scores[county] = (score / script_count) * min(script_count / 5, 1.0)  # Volume bonus capped

        return expertise_scores

    def get_event_prefix_analysis(self) -> Dict[str, Any]:
        """Analyze event prefix usage across counties"""

        analysis = {
            'prefix_usage': {},
            'county_prefix_diversity': {},
            'most_common_prefixes': [],
            'specialized_counties': {}
        }

        # Analyze prefix usage
        for prefix_node in self.graph.nodes():
            if prefix_node.startswith("event_prefix:"):
                prefix = prefix_node.split(":")[1]
                prefix_data = self.graph.nodes[prefix_node]

                analysis['prefix_usage'][prefix] = {
                    'usage_count': prefix_data.get('usage_count', 0),
                    'counties': list(prefix_data.get('counties', set())),
                    'description': prefix_data.get('description', '')
                }

        # County prefix diversity
        for county_node in self.graph.nodes():
            if county_node.startswith("county:"):
                county = county_node.split(":")[1]
                county_prefixes = set()

                for script_node in self.graph.successors(county_node):
                    script_data = self.graph.nodes[script_node]
                    if script_data.get('event_prefix'):
                        county_prefixes.add(script_data['event_prefix'])

                analysis['county_prefix_diversity'][county] = {
                    'prefix_count': len(county_prefixes),
                    'prefixes': list(county_prefixes)
                }

        # Most common prefixes
        prefix_counts = [(prefix, data['usage_count'])
                        for prefix, data in analysis['prefix_usage'].items()]
        analysis['most_common_prefixes'] = sorted(prefix_counts, key=lambda x: x[1], reverse=True)

        # Find specialized counties (counties with unique prefix combinations)
        for county, data in analysis['county_prefix_diversity'].items():
            if data['prefix_count'] >= 5:  # Counties with diverse prefix usage
                analysis['specialized_counties'][county] = data

        return analysis

    def get_module_application_matrix(self) -> Dict[str, Any]:
        """Get matrix of modules vs application types across counties"""

        matrix = {}
        county_coverage = {}

        for script_node in self.graph.nodes():
            if script_node.startswith("script:"):
                script_data = self.graph.nodes[script_node]

                module = script_data.get('module')
                app_type = script_data.get('application_type')
                county = script_data.get('county')

                if module and app_type and county:
                    if module not in matrix:
                        matrix[module] = {}
                    if app_type not in matrix[module]:
                        matrix[module][app_type] = set()

                    matrix[module][app_type].add(county)

                    # Track county coverage
                    if county not in county_coverage:
                        county_coverage[county] = {'modules': set(), 'app_types': set()}
                    county_coverage[county]['modules'].add(module)
                    county_coverage[county]['app_types'].add(app_type)

        # Convert sets to lists for JSON serialization
        for module in matrix:
            for app_type in matrix[module]:
                matrix[module][app_type] = list(matrix[module][app_type])

        for county in county_coverage:
            county_coverage[county]['modules'] = list(county_coverage[county]['modules'])
            county_coverage[county]['app_types'] = list(county_coverage[county]['app_types'])

        return {
            'module_app_matrix': matrix,
            'county_coverage': county_coverage,
            'coverage_stats': {
                'total_modules': len(matrix),
                'total_app_types': len(set(app_type for module_data in matrix.values()
                                         for app_type in module_data.keys())),
                'avg_modules_per_county': sum(len(data['modules']) for data in county_coverage.values()) / len(county_coverage) if county_coverage else 0
            }
        }
    
    def find_implementation_patterns(self, pattern_type: str) -> Dict[str, List[str]]:
        """Find common implementation patterns across counties"""
        
        patterns = defaultdict(list)
        
        if pattern_type == "function_combinations":
            # Find common function combinations
            for script_node in self.graph.nodes():
                if not script_node.startswith("script:"):
                    continue
                    
                functions = []
                for func_node in self.graph.successors(script_node):
                    if func_node.startswith("function:"):
                        functions.append(func_node.split(":")[1])
                
                if len(functions) >= 2:
                    # Create pattern from function combinations
                    functions.sort()
                    pattern = " + ".join(functions[:3])  # Top 3 functions
                    county = self.graph.nodes[script_node]['county']
                    patterns[pattern].append(county)
        
        # Filter patterns that appear in multiple counties
        common_patterns = {pattern: counties for pattern, counties in patterns.items() 
                          if len(set(counties)) >= 2}
        
        return common_patterns
    
    def export_graph(self, filename: str = "accela_knowledge_graph.gexf"):
        """Export graph for visualization"""
        nx.write_gexf(self.graph, filename)
        print(f"📊 Graph exported to {filename}")
    
    def get_graph_stats(self) -> Dict[str, Any]:
        """Get comprehensive graph statistics"""
        
        stats = {
            'nodes': self.graph.number_of_nodes(),
            'edges': self.graph.number_of_edges(),
            'counties': len([n for n in self.graph.nodes() if n.startswith("county:")]),
            'functions': len([n for n in self.graph.nodes() if n.startswith("function:")]),
            'scripts': len([n for n in self.graph.nodes() if n.startswith("script:")]),
            'modules': len([n for n in self.graph.nodes() if n.startswith("module:")]),
            'density': nx.density(self.graph),
            'avg_clustering': nx.average_clustering(self.graph.to_undirected())
        }
        
        return stats
