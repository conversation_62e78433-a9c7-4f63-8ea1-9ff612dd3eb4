#!/usr/bin/env python3
"""
Agentic Graph-Based Knowledge System for Accela Implementations
Uses intelligent agents and graph knowledge to find optimal solutions across counties
"""

import networkx as nx
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, Counter
import requests
from abc import ABC, abstractmethod

class AgentRole(Enum):
    """Different types of agents in the system"""
    ANALYZER = "analyzer"           # Analyzes code patterns and relationships
    COMPARATOR = "comparator"       # Compares implementations across counties
    RECOMMENDER = "recommender"     # Recommends best practices
    SYNTHESIZER = "synthesizer"     # Synthesizes solutions from multiple sources
    VALIDATOR = "validator"         # Validates recommendations

@dataclass
class KnowledgeNode:
    """Represents a node in the knowledge graph"""
    id: str
    type: str  # county, function, script_type, module, pattern
    properties: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class KnowledgeEdge:
    """Represents an edge in the knowledge graph"""
    source: str
    target: str
    relationship: str  # implements, uses, similar_to, depends_on, best_practice
    weight: float = 1.0
    properties: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AgentTask:
    """Task for an agent to execute"""
    task_id: str
    agent_role: AgentRole
    query: str
    context: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    priority: int = 1

@dataclass
class AgentResponse:
    """Response from an agent"""
    task_id: str
    agent_role: AgentRole
    result: Dict[str, Any]
    confidence: float
    reasoning: str
    recommendations: List[str] = field(default_factory=list)

class AccelaKnowledgeGraph:
    """Graph-based knowledge representation for Accela implementations"""
    
    def __init__(self):
        self.graph = nx.MultiDiGraph()
        self.kb_client = None
        
    def set_kb_client(self, client):
        """Set the knowledge base client"""
        self.kb_client = client
        
    def build_graph_from_metadata(self, metadata_file: str = "accela_metadata.json"):
        """Build knowledge graph from extracted metadata"""
        print("🔗 Building knowledge graph from metadata...")
        
        with open(metadata_file, 'r') as f:
            scripts_data = json.load(f)
        
        # Add nodes for counties, functions, modules, etc.
        counties = set()
        functions = set()
        modules = set()
        script_types = set()
        
        for script in scripts_data:
            county = script['county']
            counties.add(county)
            
            # Add county node
            if not self.graph.has_node(f"county:{county}"):
                self.graph.add_node(f"county:{county}", 
                                  type="county", 
                                  name=county,
                                  script_count=0,
                                  complexity_avg=0)
            
            # Update county stats
            self.graph.nodes[f"county:{county}"]['script_count'] += 1
            
            # Add script node
            script_id = f"script:{script['file_path']}"
            self.graph.add_node(script_id,
                              type="script",
                              county=county,
                              script_type=script['script_type'],
                              module=script.get('module'),
                              complexity=script['complexity'],
                              doc_quality=script['documentation_quality'],
                              functions=script.get('functions', []))
            
            # Connect county to script
            self.graph.add_edge(f"county:{county}", script_id, relationship="contains")
            
            # Add function nodes and relationships
            for func in script.get('functions', []):
                func_id = f"function:{func}"
                functions.add(func)
                
                if not self.graph.has_node(func_id):
                    self.graph.add_node(func_id, 
                                      type="function", 
                                      name=func,
                                      usage_count=0,
                                      counties=set())
                
                # Update function stats
                self.graph.nodes[func_id]['usage_count'] += 1
                self.graph.nodes[func_id]['counties'].add(county)
                
                # Connect script to function
                self.graph.add_edge(script_id, func_id, relationship="implements")
                
            # Add module relationships
            if script.get('module'):
                module = script['module']
                module_id = f"module:{module}"
                modules.add(module)
                
                if not self.graph.has_node(module_id):
                    self.graph.add_node(module_id, type="module", name=module)
                
                self.graph.add_edge(script_id, module_id, relationship="belongs_to")
        
        # Calculate similarity relationships
        self._calculate_similarities()
        
        print(f"✅ Graph built: {self.graph.number_of_nodes()} nodes, {self.graph.number_of_edges()} edges")
        print(f"   Counties: {len(counties)}, Functions: {len(functions)}, Modules: {len(modules)}")
        
    def _calculate_similarities(self):
        """Calculate similarity relationships between counties and functions"""
        
        # County similarities based on shared functions
        counties = [n for n in self.graph.nodes() if n.startswith("county:")]
        
        for i, county1 in enumerate(counties):
            for county2 in counties[i+1:]:
                similarity = self._calculate_county_similarity(county1, county2)
                if similarity > 0.3:  # Threshold for similarity
                    self.graph.add_edge(county1, county2, 
                                      relationship="similar_to", 
                                      weight=similarity)
        
        # Function similarities based on co-occurrence
        functions = [n for n in self.graph.nodes() if n.startswith("function:")]
        
        for i, func1 in enumerate(functions):
            for func2 in functions[i+1:]:
                similarity = self._calculate_function_similarity(func1, func2)
                if similarity > 0.4:
                    self.graph.add_edge(func1, func2,
                                      relationship="often_used_with",
                                      weight=similarity)
    
    def _calculate_county_similarity(self, county1: str, county2: str) -> float:
        """Calculate similarity between two counties based on shared functions"""
        
        # Get functions used by each county
        county1_functions = set()
        county2_functions = set()
        
        for script in self.graph.successors(county1):
            for func in self.graph.successors(script):
                if func.startswith("function:"):
                    county1_functions.add(func)
        
        for script in self.graph.successors(county2):
            for func in self.graph.successors(script):
                if func.startswith("function:"):
                    county2_functions.add(func)
        
        # Jaccard similarity
        if not county1_functions and not county2_functions:
            return 0.0
        
        intersection = len(county1_functions.intersection(county2_functions))
        union = len(county1_functions.union(county2_functions))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_function_similarity(self, func1: str, func2: str) -> float:
        """Calculate similarity between functions based on co-occurrence in scripts"""
        
        # Get scripts that use each function
        func1_scripts = set(self.graph.predecessors(func1))
        func2_scripts = set(self.graph.predecessors(func2))
        
        if not func1_scripts or not func2_scripts:
            return 0.0
        
        # Jaccard similarity
        intersection = len(func1_scripts.intersection(func2_scripts))
        union = len(func1_scripts.union(func2_scripts))
        
        return intersection / union if union > 0 else 0.0
    
    def find_best_implementations(self, use_case: str, criteria: Dict[str, float] = None) -> List[Dict]:
        """Find best implementations for a specific use case"""
        
        if criteria is None:
            criteria = {
                'complexity': 0.3,      # Lower complexity preferred
                'doc_quality': 0.4,     # Higher doc quality preferred
                'usage_frequency': 0.3   # Higher usage preferred
            }
        
        # Search for relevant scripts using semantic search
        if self.kb_client:
            search_results = self.kb_client.semantic_search(use_case, k=20)
        else:
            search_results = []
        
        # Score implementations
        scored_implementations = []
        
        for result in search_results:
            script_path = result['metadata']['file_path']
            script_id = f"script:{script_path}"
            
            if self.graph.has_node(script_id):
                score = self._score_implementation(script_id, criteria)
                
                scored_implementations.append({
                    'script_id': script_id,
                    'county': result['metadata']['county'],
                    'score': score,
                    'content': result['content'],
                    'metadata': result['metadata'],
                    'reasoning': self._explain_score(script_id, criteria, score)
                })
        
        # Sort by score
        scored_implementations.sort(key=lambda x: x['score'], reverse=True)
        
        return scored_implementations
    
    def _score_implementation(self, script_id: str, criteria: Dict[str, float]) -> float:
        """Score an implementation based on criteria"""
        
        if not self.graph.has_node(script_id):
            return 0.0
        
        node_data = self.graph.nodes[script_id]
        score = 0.0
        
        # Complexity score (lower is better)
        complexity_map = {'low': 1.0, 'medium': 0.6, 'high': 0.2}
        complexity_score = complexity_map.get(node_data.get('complexity', 'medium'), 0.6)
        score += criteria.get('complexity', 0.3) * complexity_score
        
        # Documentation quality score
        doc_map = {'excellent': 1.0, 'good': 0.7, 'poor': 0.3}
        doc_score = doc_map.get(node_data.get('doc_quality', 'poor'), 0.3)
        score += criteria.get('doc_quality', 0.4) * doc_score
        
        # Usage frequency (based on function popularity)
        usage_score = 0.0
        functions = node_data.get('functions', [])
        if functions:
            for func in functions:
                func_id = f"function:{func}"
                if self.graph.has_node(func_id):
                    func_usage = self.graph.nodes[func_id].get('usage_count', 0)
                    usage_score += func_usage
            usage_score = min(usage_score / len(functions) / 10, 1.0)  # Normalize
        
        score += criteria.get('usage_frequency', 0.3) * usage_score
        
        return score
    
    def _explain_score(self, script_id: str, criteria: Dict[str, float], score: float) -> str:
        """Explain why a script received its score"""
        
        node_data = self.graph.nodes[script_id]
        explanations = []
        
        explanations.append(f"Overall Score: {score:.2f}")
        explanations.append(f"Complexity: {node_data.get('complexity', 'unknown')}")
        explanations.append(f"Documentation: {node_data.get('doc_quality', 'unknown')}")
        explanations.append(f"Functions: {len(node_data.get('functions', []))}")
        
        return " | ".join(explanations)
    
    def get_county_expertise(self, domain: str) -> Dict[str, float]:
        """Get expertise scores for each county in a specific domain"""
        
        expertise_scores = {}
        
        for county_node in self.graph.nodes():
            if not county_node.startswith("county:"):
                continue
                
            county = county_node.split(":")[1]
            score = 0.0
            script_count = 0
            
            # Analyze scripts in this domain
            for script_node in self.graph.successors(county_node):
                script_data = self.graph.nodes[script_node]
                
                # Check if script is relevant to domain
                if (domain.lower() in str(script_data.get('module', '')).lower() or
                    domain.lower() in str(script_data.get('script_type', '')).lower()):
                    
                    script_count += 1
                    
                    # Add to score based on quality
                    complexity_bonus = {'low': 0.3, 'medium': 0.2, 'high': 0.1}
                    doc_bonus = {'excellent': 0.4, 'good': 0.2, 'poor': 0.1}
                    
                    score += complexity_bonus.get(script_data.get('complexity', 'medium'), 0.2)
                    score += doc_bonus.get(script_data.get('doc_quality', 'poor'), 0.1)
            
            if script_count > 0:
                expertise_scores[county] = score / script_count * script_count  # Weighted by volume
        
        return expertise_scores
    
    def find_implementation_patterns(self, pattern_type: str) -> Dict[str, List[str]]:
        """Find common implementation patterns across counties"""
        
        patterns = defaultdict(list)
        
        if pattern_type == "function_combinations":
            # Find common function combinations
            for script_node in self.graph.nodes():
                if not script_node.startswith("script:"):
                    continue
                    
                functions = []
                for func_node in self.graph.successors(script_node):
                    if func_node.startswith("function:"):
                        functions.append(func_node.split(":")[1])
                
                if len(functions) >= 2:
                    # Create pattern from function combinations
                    functions.sort()
                    pattern = " + ".join(functions[:3])  # Top 3 functions
                    county = self.graph.nodes[script_node]['county']
                    patterns[pattern].append(county)
        
        # Filter patterns that appear in multiple counties
        common_patterns = {pattern: counties for pattern, counties in patterns.items() 
                          if len(set(counties)) >= 2}
        
        return common_patterns
    
    def export_graph(self, filename: str = "accela_knowledge_graph.gexf"):
        """Export graph for visualization"""
        nx.write_gexf(self.graph, filename)
        print(f"📊 Graph exported to {filename}")
    
    def get_graph_stats(self) -> Dict[str, Any]:
        """Get comprehensive graph statistics"""
        
        stats = {
            'nodes': self.graph.number_of_nodes(),
            'edges': self.graph.number_of_edges(),
            'counties': len([n for n in self.graph.nodes() if n.startswith("county:")]),
            'functions': len([n for n in self.graph.nodes() if n.startswith("function:")]),
            'scripts': len([n for n in self.graph.nodes() if n.startswith("script:")]),
            'modules': len([n for n in self.graph.nodes() if n.startswith("module:")]),
            'density': nx.density(self.graph),
            'avg_clustering': nx.average_clustering(self.graph.to_undirected())
        }
        
        return stats
