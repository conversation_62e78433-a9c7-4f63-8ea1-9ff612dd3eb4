#!/usr/bin/env python3
"""
Accela Knowledge Base Setup Script
Creates a comprehensive knowledge base from multiple county Accela implementations
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import hashlib
from datetime import datetime

@dataclass
class ScriptMetadata:
    """Metadata structure for Accela scripts"""
    file_path: str
    county: str
    script_type: str  # event, batch, interface, set, pageflow, expression
    module: Optional[str] = None  # permits, licenses, planning, etc.
    app_type: Optional[str] = None  # building, zoning, business, etc.
    functions: List[str] = None
    dependencies: List[str] = None
    last_modified: str = None
    complexity: str = "medium"  # low, medium, high
    documentation_quality: str = "poor"  # poor, good, excellent
    file_hash: str = None
    content: str = None

    def __post_init__(self):
        if self.functions is None:
            self.functions = []
        if self.dependencies is None:
            self.dependencies = []

class AccelaScriptAnalyzer:
    """Analyzes Accela JavaScript files and extracts metadata"""
    
    def __init__(self, src_directory: str = "src"):
        self.src_dir = Path(src_directory)
        self.county_mappings = {
            "accela-masterscripts": "asheville",
            "Accela": "santa_barbara", 
            "AccelaProd": "dayton",
            "LeonAccelaScripts": "leon",
            "MyAccela": "atlanta_chattanooga",
            "AccelaSupp": "lancaster",
            "COKApplications-Accela": "cok",
            "AccelaDEV": "ep_support",
            "EMSE_DEV_3_0": "solano",
            "development": "development"
        }
        
    def extract_functions(self, content: str) -> List[str]:
        """Extract function names from JavaScript content"""
        functions = []
        
        # Function declarations: function functionName()
        func_pattern = r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\('
        functions.extend(re.findall(func_pattern, content))
        
        # Variable function assignments: var functionName = function()
        var_func_pattern = r'var\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*function'
        functions.extend(re.findall(var_func_pattern, content))
        
        return list(set(functions))  # Remove duplicates
    
    def extract_dependencies(self, content: str) -> List[str]:
        """Extract dependencies and includes from script content"""
        dependencies = []
        
        # Look for include patterns
        include_patterns = [
            r'#include\s+["\']([^"\']+)["\']',
            r'eval\(getScriptText\(["\']([^"\']+)["\']',
            r'loadScript\(["\']([^"\']+)["\']'
        ]
        
        for pattern in include_patterns:
            dependencies.extend(re.findall(pattern, content))
            
        return list(set(dependencies))
    
    def determine_script_type(self, file_path: Path) -> str:
        """Determine script type based on directory structure"""
        path_parts = file_path.parts
        
        for part in path_parts:
            part_lower = part.lower()
            if part_lower in ['event', 'events']:
                return 'event'
            elif part_lower in ['batch', 'batchs']:
                return 'batch'
            elif part_lower in ['interface', 'interfaces']:
                return 'interface'
            elif part_lower in ['set', 'sets']:
                return 'set'
            elif part_lower in ['pageflow', 'pageflows']:
                return 'pageflow'
            elif part_lower in ['expression', 'expressions']:
                return 'expression'
            elif part_lower in ['misc', 'miscellaneous']:
                return 'misc'
                
        return 'unknown'
    
    def determine_module_and_app_type(self, content: str, filename: str) -> tuple:
        """Determine module and application type from content and filename"""
        content_lower = content.lower()
        filename_lower = filename.lower()
        
        # Module detection
        module = None
        if any(term in content_lower for term in ['permit', 'building', 'construction']):
            module = 'permits'
        elif any(term in content_lower for term in ['license', 'business']):
            module = 'licenses'
        elif any(term in content_lower for term in ['planning', 'zoning']):
            module = 'planning'
        elif any(term in content_lower for term in ['enforcement', 'violation']):
            module = 'enforcement'
        elif any(term in content_lower for term in ['inspection']):
            module = 'inspections'
            
        # App type detection
        app_type = None
        if any(term in content_lower for term in ['building', 'construction']):
            app_type = 'building'
        elif any(term in content_lower for term in ['zoning']):
            app_type = 'zoning'
        elif any(term in content_lower for term in ['business', 'license']):
            app_type = 'business'
        elif any(term in content_lower for term in ['planning']):
            app_type = 'planning'
            
        return module, app_type
    
    def calculate_complexity(self, content: str, functions: List[str]) -> str:
        """Calculate script complexity based on various factors"""
        lines = len(content.split('\n'))
        function_count = len(functions)
        
        # Simple heuristic for complexity
        if lines < 50 and function_count < 3:
            return 'low'
        elif lines < 200 and function_count < 10:
            return 'medium'
        else:
            return 'high'
    
    def assess_documentation_quality(self, content: str) -> str:
        """Assess the quality of documentation in the script"""
        comment_lines = len([line for line in content.split('\n') 
                           if line.strip().startswith('//') or 
                              line.strip().startswith('/*') or
                              line.strip().startswith('*')])
        total_lines = len(content.split('\n'))
        
        if total_lines == 0:
            return 'poor'
            
        comment_ratio = comment_lines / total_lines
        
        if comment_ratio > 0.3:
            return 'excellent'
        elif comment_ratio > 0.1:
            return 'good'
        else:
            return 'poor'
    
    def analyze_script(self, file_path: Path) -> Optional[ScriptMetadata]:
        """Analyze a single Accela script file"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # Determine county from path
            county = 'unknown'
            for repo_name, county_name in self.county_mappings.items():
                if repo_name in str(file_path):
                    county = county_name
                    break
            
            # Extract metadata
            functions = self.extract_functions(content)
            dependencies = self.extract_dependencies(content)
            script_type = self.determine_script_type(file_path)
            module, app_type = self.determine_module_and_app_type(content, file_path.name)
            complexity = self.calculate_complexity(content, functions)
            doc_quality = self.assess_documentation_quality(content)
            
            # File hash for change detection
            file_hash = hashlib.md5(content.encode()).hexdigest()
            
            # Last modified time
            last_modified = datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
            
            return ScriptMetadata(
                file_path=str(file_path),
                county=county,
                script_type=script_type,
                module=module,
                app_type=app_type,
                functions=functions,
                dependencies=dependencies,
                last_modified=last_modified,
                complexity=complexity,
                documentation_quality=doc_quality,
                file_hash=file_hash,
                content=content
            )
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return None
    
    def scan_all_scripts(self) -> List[ScriptMetadata]:
        """Scan all JavaScript files in the source directory"""
        scripts = []
        
        # Find all .js files
        js_files = list(self.src_dir.rglob("*.js"))
        
        print(f"Found {len(js_files)} JavaScript files to analyze...")
        
        for js_file in js_files:
            metadata = self.analyze_script(js_file)
            if metadata:
                scripts.append(metadata)
                
        print(f"Successfully analyzed {len(scripts)} scripts")
        return scripts
    
    def save_metadata(self, scripts: List[ScriptMetadata], output_file: str = "accela_metadata.json"):
        """Save extracted metadata to JSON file"""
        metadata_dict = [asdict(script) for script in scripts]
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_dict, f, indent=2, ensure_ascii=False)
            
        print(f"Metadata saved to {output_file}")

def main():
    """Main execution function"""
    analyzer = AccelaScriptAnalyzer()
    
    print("Starting Accela Knowledge Base extraction...")
    scripts = analyzer.scan_all_scripts()
    
    # Save metadata
    analyzer.save_metadata(scripts)
    
    # Print summary statistics
    print("\n=== SUMMARY STATISTICS ===")
    print(f"Total scripts analyzed: {len(scripts)}")
    
    # County breakdown
    county_counts = {}
    for script in scripts:
        county_counts[script.county] = county_counts.get(script.county, 0) + 1
    
    print("\nScripts by County:")
    for county, count in sorted(county_counts.items()):
        print(f"  {county}: {count}")
    
    # Script type breakdown
    type_counts = {}
    for script in scripts:
        type_counts[script.script_type] = type_counts.get(script.script_type, 0) + 1
    
    print("\nScripts by Type:")
    for script_type, count in sorted(type_counts.items()):
        print(f"  {script_type}: {count}")

if __name__ == "__main__":
    main()
