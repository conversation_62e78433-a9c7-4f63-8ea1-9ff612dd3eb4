#!/usr/bin/env python3
"""
Knowledge Graph Visualization Tool
Provides multiple ways to visualize and explore the Accela knowledge graph
"""

import json
import networkx as nx
from pathlib import Path
import sys
import os

# Add the package to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from accela_knowledge_base.knowledge_graph.graph_builder import AccelaKnowledgeGraph
from accela_knowledge_base.core.config import Config


def clean_graph_for_export(graph):
    """Clean graph data to remove None values that cause GEXF export issues"""
    
    cleaned_graph = graph.copy()
    
    # Clean node attributes
    for node in cleaned_graph.nodes():
        node_data = cleaned_graph.nodes[node]
        cleaned_data = {}
        
        for key, value in node_data.items():
            if value is not None:
                if isinstance(value, list):
                    # Convert lists to comma-separated strings
                    cleaned_data[key] = ','.join(str(v) for v in value)
                else:
                    cleaned_data[key] = str(value)
        
        # Update node with cleaned data
        cleaned_graph.nodes[node].clear()
        cleaned_graph.nodes[node].update(cleaned_data)
    
    # Clean edge attributes
    for edge in cleaned_graph.edges(data=True):
        edge_data = edge[2]
        cleaned_data = {}
        
        for key, value in edge_data.items():
            if value is not None:
                cleaned_data[key] = str(value)
        
        # Update edge with cleaned data
        edge_data.clear()
        edge_data.update(cleaned_data)
    
    return cleaned_graph


def export_to_gexf(graph, filename="accela_knowledge_graph.gexf"):
    """Export graph to GEXF format for Gephi"""
    
    try:
        cleaned_graph = clean_graph_for_export(graph)
        nx.write_gexf(cleaned_graph, filename)
        print(f"✅ GEXF export successful: {filename}")
        print(f"   📊 Nodes: {cleaned_graph.number_of_nodes()}")
        print(f"   📊 Edges: {cleaned_graph.number_of_edges()}")
        return True
    except Exception as e:
        print(f"❌ GEXF export failed: {e}")
        return False


def export_to_json(graph, filename="accela_knowledge_graph.json"):
    """Export graph to JSON format for web visualization"""
    
    try:
        # Convert to JSON-serializable format
        graph_data = {
            'nodes': [],
            'edges': []
        }
        
        # Add nodes
        for node in graph.nodes():
            node_data = graph.nodes[node].copy()
            
            # Clean node data
            cleaned_node = {'id': node}
            for key, value in node_data.items():
                if value is not None:
                    if isinstance(value, list):
                        cleaned_node[key] = value
                    else:
                        cleaned_node[key] = value
            
            graph_data['nodes'].append(cleaned_node)
        
        # Add edges
        for source, target, data in graph.edges(data=True):
            edge = {
                'source': source,
                'target': target
            }
            
            # Add edge attributes
            for key, value in data.items():
                if value is not None:
                    edge[key] = value
            
            graph_data['edges'].append(edge)
        
        # Save to JSON
        with open(filename, 'w') as f:
            json.dump(graph_data, f, indent=2)
        
        print(f"✅ JSON export successful: {filename}")
        print(f"   📊 Nodes: {len(graph_data['nodes'])}")
        print(f"   📊 Edges: {len(graph_data['edges'])}")
        return True
        
    except Exception as e:
        print(f"❌ JSON export failed: {e}")
        return False


def export_to_cytoscape(graph, filename="accela_cytoscape.json"):
    """Export graph to Cytoscape.js format"""
    
    try:
        cytoscape_data = {
            'elements': {
                'nodes': [],
                'edges': []
            }
        }
        
        # Add nodes
        for node in graph.nodes():
            node_data = graph.nodes[node].copy()
            
            # Create Cytoscape node
            cy_node = {
                'data': {
                    'id': node,
                    'label': node.split(':')[-1] if ':' in node else node
                }
            }
            
            # Add node attributes
            for key, value in node_data.items():
                if value is not None:
                    if isinstance(value, list):
                        cy_node['data'][key] = ','.join(str(v) for v in value)
                    else:
                        cy_node['data'][key] = str(value)
            
            cytoscape_data['elements']['nodes'].append(cy_node)
        
        # Add edges
        for source, target, data in graph.edges(data=True):
            cy_edge = {
                'data': {
                    'id': f"{source}-{target}",
                    'source': source,
                    'target': target
                }
            }
            
            # Add edge attributes
            for key, value in data.items():
                if value is not None:
                    cy_edge['data'][key] = str(value)
            
            cytoscape_data['elements']['edges'].append(cy_edge)
        
        # Save to JSON
        with open(filename, 'w') as f:
            json.dump(cytoscape_data, f, indent=2)
        
        print(f"✅ Cytoscape export successful: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Cytoscape export failed: {e}")
        return False


def create_html_viewer(json_filename="accela_knowledge_graph.json", html_filename="graph_viewer.html"):
    """Create an HTML viewer for the graph"""
    
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>Accela Knowledge Graph Viewer</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
        #graph {{ border: 1px solid #ccc; }}
        .controls {{ margin-bottom: 20px; }}
        .node {{ fill: #69b3a2; stroke: #fff; stroke-width: 2px; }}
        .node.county {{ fill: #ff6b6b; }}
        .node.script {{ fill: #4ecdc4; }}
        .node.function {{ fill: #45b7d1; }}
        .node.event_prefix {{ fill: #96ceb4; }}
        .link {{ stroke: #999; stroke-opacity: 0.6; stroke-width: 1px; }}
        .tooltip {{ position: absolute; padding: 10px; background: rgba(0,0,0,0.8); color: white; border-radius: 5px; pointer-events: none; }}
    </style>
</head>
<body>
    <h1>🎯 Accela Knowledge Graph Visualization</h1>
    
    <div class="controls">
        <button onclick="zoomToFit()">Zoom to Fit</button>
        <button onclick="resetZoom()">Reset Zoom</button>
        <label>Filter by type: 
            <select id="nodeTypeFilter" onchange="filterNodes()">
                <option value="">All</option>
                <option value="county">Counties</option>
                <option value="script">Scripts</option>
                <option value="function">Functions</option>
                <option value="event_prefix">Event Prefixes</option>
            </select>
        </label>
    </div>
    
    <svg id="graph" width="1200" height="800"></svg>
    
    <script>
        // Load and visualize the graph
        d3.json("{json_filename}").then(function(data) {{
            const svg = d3.select("#graph");
            const width = +svg.attr("width");
            const height = +svg.attr("height");
            
            // Create tooltip
            const tooltip = d3.select("body").append("div")
                .attr("class", "tooltip")
                .style("opacity", 0);
            
            // Create zoom behavior
            const zoom = d3.zoom()
                .scaleExtent([0.1, 10])
                .on("zoom", function(event) {{
                    container.attr("transform", event.transform);
                }});
            
            svg.call(zoom);
            
            const container = svg.append("g");
            
            // Create force simulation
            const simulation = d3.forceSimulation(data.nodes)
                .force("link", d3.forceLink(data.edges).id(d => d.id).distance(100))
                .force("charge", d3.forceManyBody().strength(-300))
                .force("center", d3.forceCenter(width / 2, height / 2));
            
            // Create links
            const link = container.append("g")
                .selectAll("line")
                .data(data.edges)
                .enter().append("line")
                .attr("class", "link");
            
            // Create nodes
            const node = container.append("g")
                .selectAll("circle")
                .data(data.nodes)
                .enter().append("circle")
                .attr("class", d => `node ${{d.type || 'default'}}`)
                .attr("r", d => {{
                    if (d.type === 'county') return 15;
                    if (d.type === 'script') return 8;
                    if (d.type === 'function') return 5;
                    return 6;
                }})
                .on("mouseover", function(event, d) {{
                    tooltip.transition().duration(200).style("opacity", .9);
                    tooltip.html(`
                        <strong>${{d.id}}</strong><br/>
                        Type: ${{d.type || 'unknown'}}<br/>
                        ${{d.county ? `County: ${{d.county}}<br/>` : ''}}
                        ${{d.functions ? `Functions: ${{d.functions.split(',').length}}<br/>` : ''}}
                    `)
                    .style("left", (event.pageX + 10) + "px")
                    .style("top", (event.pageY - 28) + "px");
                }})
                .on("mouseout", function(d) {{
                    tooltip.transition().duration(500).style("opacity", 0);
                }})
                .call(d3.drag()
                    .on("start", dragstarted)
                    .on("drag", dragged)
                    .on("end", dragended));
            
            // Add labels for important nodes
            const label = container.append("g")
                .selectAll("text")
                .data(data.nodes.filter(d => d.type === 'county'))
                .enter().append("text")
                .text(d => d.id.replace('county:', ''))
                .style("font-size", "12px")
                .style("text-anchor", "middle");
            
            // Update positions on simulation tick
            simulation.on("tick", function() {{
                link
                    .attr("x1", d => d.source.x)
                    .attr("y1", d => d.source.y)
                    .attr("x2", d => d.target.x)
                    .attr("y2", d => d.target.y);
                
                node
                    .attr("cx", d => d.x)
                    .attr("cy", d => d.y);
                
                label
                    .attr("x", d => d.x)
                    .attr("y", d => d.y + 25);
            }});
            
            // Drag functions
            function dragstarted(event, d) {{
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }}
            
            function dragged(event, d) {{
                d.fx = event.x;
                d.fy = event.y;
            }}
            
            function dragended(event, d) {{
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }}
            
            // Global functions
            window.zoomToFit = function() {{
                const bounds = container.node().getBBox();
                const fullWidth = width;
                const fullHeight = height;
                const widthScale = fullWidth / bounds.width;
                const heightScale = fullHeight / bounds.height;
                const scale = 0.8 * Math.min(widthScale, heightScale);
                const translate = [fullWidth / 2 - scale * (bounds.x + bounds.width / 2), fullHeight / 2 - scale * (bounds.y + bounds.height / 2)];
                
                svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
            }};
            
            window.resetZoom = function() {{
                svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity);
            }};
            
            window.filterNodes = function() {{
                const filterType = document.getElementById('nodeTypeFilter').value;
                node.style('opacity', d => filterType === '' || d.type === filterType ? 1 : 0.1);
                link.style('opacity', d => {{
                    if (filterType === '') return 0.6;
                    return (d.source.type === filterType || d.target.type === filterType) ? 0.6 : 0.1;
                }});
            }};
        }});
    </script>
</body>
</html>
"""
    
    with open(html_filename, 'w') as f:
        f.write(html_content)
    
    print(f"✅ HTML viewer created: {html_filename}")
    print(f"   🌐 Open in browser: file://{Path(html_filename).absolute()}")


def main():
    """Main visualization function"""
    
    print("🎨 Accela Knowledge Graph Visualization Tool")
    print("=" * 50)
    
    # Load configuration and build graph
    print("📊 Loading knowledge graph...")
    config = Config.from_env()
    graph = AccelaKnowledgeGraph(config)
    graph.build_from_metadata()
    
    stats = graph.get_stats()
    print(f"✅ Graph loaded: {stats['nodes']} nodes, {stats['edges']} edges")
    
    # Export in multiple formats
    print("\\n📤 Exporting graph in multiple formats...")
    
    # 1. GEXF for Gephi
    export_to_gexf(graph.graph, "accela_knowledge_graph.gexf")
    
    # 2. JSON for web visualization
    export_to_json(graph.graph, "accela_knowledge_graph.json")
    
    # 3. Cytoscape format
    export_to_cytoscape(graph.graph, "accela_cytoscape.json")
    
    # 4. HTML viewer
    create_html_viewer()
    
    print("\\n🎯 Visualization Options:")
    print("1. 📊 Gephi: Open 'accela_knowledge_graph.gexf' in Gephi")
    print("2. 🌐 Web Viewer: Open 'graph_viewer.html' in browser")
    print("3. 📱 Cytoscape: Use 'accela_cytoscape.json' with Cytoscape.js")
    print("4. ☁️  Neo4j: Import JSON data to Neo4j for cloud visualization")
    
    print("\\n🚀 Cloud Options:")
    print("• Neo4j Aura: https://neo4j.com/cloud/aura/")
    print("• Graphistry: https://www.graphistry.com/")
    print("• Linkurious: https://linkurio.us/")


if __name__ == "__main__":
    main()
